import { DeepseekProvider, DeepseekConfig } from '@/providers/deepseek';
import { OllamaProvider, OllamaConfig } from '@/providers/ollama';
import { LLMMessage, ToolDefinition } from '@/types';
export type LLMProviderInstance = DeepseekProvider | OllamaProvider;
export declare class LLMManager {
    private provider;
    private configManager;
    constructor();
    initializeProvider(): Promise<void>;
    generateResponse(messages: LLMMessage[], tools?: ToolDefinition[], options?: {
        maxTokens?: number;
        temperature?: number;
        stream?: boolean;
    }): Promise<LLMMessage>;
    validateConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    getCurrentProvider(): string;
    getCurrentModel(): string;
    switchProvider(provider: 'deepseek' | 'ollama', model?: string): Promise<void>;
    switchModel(model: string): Promise<void>;
    updateProviderConfig(config: Partial<DeepseekConfig | OllamaConfig>): void;
    getProviderConfig(): DeepseekConfig | OllamaConfig | null;
    isInitialized(): boolean;
    reinitialize(): Promise<void>;
    pullModel(modelName: string): Promise<void>;
    deleteModel(modelName: string): Promise<void>;
    getModelInfo(modelName: string): Promise<any>;
}
//# sourceMappingURL=llm-manager.d.ts.map