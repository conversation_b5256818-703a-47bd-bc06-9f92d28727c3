{"version": 3, "file": "onboarding.js", "sourceRoot": "", "sources": ["../../src/components/onboarding.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,EAAuB,aAAa,EAAE,MAAM,SAAS,CAAC;AAS7D,MAAM,OAAO,cAAc;IACjB,aAAa,CAAgB;IAC7B,UAAU,CAAa;IAE/B;QACE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC;YACH,8BAA8B;YAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE,CAAC;gBACtC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtD,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACvB,OAAO;wBACL,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;wBACnC,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,8BAA8B;qBACxC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEtD,kBAAkB;YAClB,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAElC,qBAAqB;YACrB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAElC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC,CAAC;YAEtE,8CAA8C;YAC9C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAE9C,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC,CAAC;gBAC/D,OAAO;oBACL,MAAM,EAAE,MAAgB;oBACxB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,sCAAsC;oBAC/C,SAAS,EAAE,IAAI;iBAChB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC,CAAC;gBACpF,OAAO;oBACL,MAAM,EAAE,MAAgB;oBACxB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,sCAAsC;oBAC/C,SAAS,EAAE,KAAK;iBACjB,CAAC;YACJ,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,6BAA6B,YAAY,IAAI,CAAC,CAAC,CAAC;YAEtE,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;gBACnC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAC5C;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,2DAA2D;gBACpE,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAC1C;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,uDAAuD;gBAChE,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC;QAEhE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YACzC;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,oCAAoC;gBAC7C,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,wEAAwE;wBACxG,KAAK,EAAE,UAAU;qBAClB;oBACD;wBACE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,4CAA4C;wBACzE,KAAK,EAAE,QAAQ;qBAChB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAA+B;QAC7D,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5B,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC,CAAC;QAEnG,MAAM,SAAS,GAAG;YAChB;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE,GAAG;gBACT,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;wBAClB,OAAO,qBAAqB,CAAC;oBAC/B,CAAC;oBACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC7B,OAAO,8CAA8C,CAAC;oBACxD,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,6CAA6C,EAAE,KAAK,EAAE,eAAe,EAAE;oBAC/E,EAAE,IAAI,EAAE,kDAAkD,EAAE,KAAK,EAAE,mBAAmB,EAAE;iBACzF;gBACD,OAAO,EAAE,eAAe;aACzB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,qCAAqC;gBAC9C,OAAO,EAAE,6BAA6B;aACvC;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,8BAA8B;gBACvC,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK;aACzD;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,kDAAkD;gBAC3D,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;aACtD;SACF,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjD,OAAO;YACL,QAAQ,EAAE,UAAU;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,6BAA6B;YACzD,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,CAAC;YAChB,OAAO,EAAE,KAAK;SACf,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC,CAAC;QACvF,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC,CAAC;QAEpE,MAAM,SAAS,GAAG;YAChB;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,wBAAwB;gBACjC,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;wBAClB,OAAO,sBAAsB,CAAC;oBAChC,CAAC;oBACD,IAAI,CAAC;wBACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;wBACf,OAAO,IAAI,CAAC;oBACd,CAAC;oBAAC,MAAM,CAAC;wBACP,OAAO,0BAA0B,CAAC;oBACpC,CAAC;gBACH,CAAC;aACF;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,gDAAgD;gBACzD,OAAO,EAAE,QAAQ;gBACjB,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;wBAClB,OAAO,wBAAwB,CAAC;oBAClC,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,8BAA8B;gBACvC,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK;aACzD;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,kDAAkD;gBAC3D,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;aACtD;SACF,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjD,OAAO;YACL,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,CAAC;YAChB,OAAO,EAAE,KAAK,CAAC,kCAAkC;SAClD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAuB;QAClD,MAAM,OAAO,GAAG,aAAa,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;QAE/E,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAEhB,qCAAqC;YACrC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAElC,+BAA+B;YAC/B,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE/D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,aAAa,CAAC,mCAAmC,CAAC,CAAC;YAC/D,CAAC;YAED,gCAAgC;YAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE1D,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAE1C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC,CAAC;YACxE,CAAC;YAED,0BAA0B;YAC1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAE5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,oDAAoD;QACpD,MAAM,MAAM,GAAoB;YAC9B,QAAQ,EAAE,UAAU;YACpB,KAAK,EAAE,eAAe;YACtB,OAAO,EAAE,6BAA6B;YACtC,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;YAChB,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,CAAC;YAChB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YACvC;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,8CAA8C;gBACvD,IAAI,EAAE,GAAG;gBACT,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;aACzE;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAElC,OAAO;gBACL,MAAM,EAAE,MAAgB;gBACxB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,oCAAoC;aAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;gBACnC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB;aACvE,CAAC;QACJ,CAAC;IACH,CAAC;CACF"}