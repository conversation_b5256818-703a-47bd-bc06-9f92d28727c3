import { ArienError } from '@/types';
import { nanoid } from 'nanoid';
import { ConfigManager } from './config';
import Conf from 'conf';
export class SessionManager {
    sessions = new Map();
    currentSessionId = null;
    configManager;
    sessionStore;
    constructor() {
        this.configManager = ConfigManager.getInstance();
        this.sessionStore = new Conf({
            projectName: 'arien-ai-cli-sessions',
            schema: {
                sessions: {
                    type: 'object',
                    default: {}
                }
            }
        });
        this.loadSessions();
    }
    loadSessions() {
        const storedSessions = this.sessionStore.get('sessions') || {};
        Object.entries(storedSessions).forEach(([id, sessionData]) => {
            // Convert date strings back to Date objects
            const data = sessionData;
            const session = {
                id: data.id,
                name: data.name,
                workingDirectory: data.workingDirectory,
                provider: data.provider,
                model: data.model,
                createdAt: new Date(data.createdAt),
                updatedAt: new Date(data.updatedAt),
                messages: (data.messages || []).map((msg) => ({
                    ...msg,
                    timestamp: new Date(msg.timestamp)
                }))
            };
            this.sessions.set(id, session);
        });
    }
    saveSessions() {
        const sessionsToStore = {};
        this.sessions.forEach((session, id) => {
            sessionsToStore[id] = session;
        });
        this.sessionStore.set('sessions', sessionsToStore);
    }
    createSession(name) {
        const config = this.configManager.getAll();
        const sessionId = nanoid();
        const sessionName = name || `Session ${new Date().toLocaleString()}`;
        const session = {
            id: sessionId,
            name: sessionName,
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            workingDirectory: config.workingDirectory || process.cwd(),
            provider: config.provider,
            model: config.model
        };
        this.sessions.set(sessionId, session);
        this.currentSessionId = sessionId;
        this.configManager.set('sessionId', sessionId);
        this.saveSessions();
        return session;
    }
    getCurrentSession() {
        if (!this.currentSessionId) {
            return null;
        }
        return this.sessions.get(this.currentSessionId) || null;
    }
    setCurrentSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new ArienError(`Session with ID ${sessionId} not found`, 'SESSION_NOT_FOUND');
        }
        this.currentSessionId = sessionId;
        this.configManager.set('sessionId', sessionId);
        return session;
    }
    getAllSessions() {
        return Array.from(this.sessions.values()).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
    }
    getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    deleteSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return false;
        }
        this.sessions.delete(sessionId);
        // If this was the current session, clear it
        if (this.currentSessionId === sessionId) {
            this.currentSessionId = null;
            this.configManager.delete('sessionId');
        }
        this.saveSessions();
        return true;
    }
    updateSessionName(sessionId, name) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return false;
        }
        session.name = name;
        session.updatedAt = new Date();
        this.saveSessions();
        return true;
    }
    addMessage(message, sessionId) {
        const targetSessionId = sessionId || this.currentSessionId;
        if (!targetSessionId) {
            throw new ArienError('No active session to add message to', 'NO_ACTIVE_SESSION');
        }
        const session = this.sessions.get(targetSessionId);
        if (!session) {
            throw new ArienError(`Session with ID ${targetSessionId} not found`, 'SESSION_NOT_FOUND');
        }
        session.messages.push(message);
        session.updatedAt = new Date();
        this.saveSessions();
    }
    getMessages(sessionId) {
        const targetSessionId = sessionId || this.currentSessionId;
        if (!targetSessionId) {
            return [];
        }
        const session = this.sessions.get(targetSessionId);
        return session ? session.messages : [];
    }
    clearMessages(sessionId) {
        const targetSessionId = sessionId || this.currentSessionId;
        if (!targetSessionId) {
            return;
        }
        const session = this.sessions.get(targetSessionId);
        if (session) {
            session.messages = [];
            session.updatedAt = new Date();
            this.saveSessions();
        }
    }
    getMessageHistory(limit, sessionId) {
        const messages = this.getMessages(sessionId);
        if (limit && limit > 0) {
            return messages.slice(-limit);
        }
        return messages;
    }
    searchMessages(query, sessionId) {
        const messages = this.getMessages(sessionId);
        const lowercaseQuery = query.toLowerCase();
        return messages.filter(message => message.content.toLowerCase().includes(lowercaseQuery));
    }
    exportSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new ArienError(`Session with ID ${sessionId} not found`, 'SESSION_NOT_FOUND');
        }
        return JSON.stringify(session, null, 2);
    }
    importSession(sessionData) {
        try {
            const parsedSession = JSON.parse(sessionData);
            // Validate and convert the session data
            const session = {
                id: parsedSession.id || nanoid(),
                name: parsedSession.name || 'Imported Session',
                messages: (parsedSession.messages || []).map((msg) => ({
                    ...msg,
                    timestamp: new Date(msg.timestamp)
                })),
                createdAt: new Date(parsedSession.createdAt || Date.now()),
                updatedAt: new Date(parsedSession.updatedAt || Date.now()),
                workingDirectory: parsedSession.workingDirectory || process.cwd(),
                provider: parsedSession.provider || 'deepseek',
                model: parsedSession.model || 'deepseek-chat'
            };
            this.sessions.set(session.id, session);
            this.saveSessions();
            return session;
        }
        catch (error) {
            throw new ArienError(`Failed to import session: ${error instanceof Error ? error.message : 'Invalid JSON'}`, 'IMPORT_ERROR');
        }
    }
    getSessionStats(sessionId) {
        const messages = this.getMessages(sessionId);
        const stats = {
            messageCount: messages.length,
            userMessages: messages.filter(m => m.role === 'user').length,
            assistantMessages: messages.filter(m => m.role === 'assistant').length,
            toolCalls: messages.reduce((count, m) => count + (m.toolCalls?.length || 0), 0),
            firstMessage: messages.length > 0 ? messages[0]?.timestamp : undefined,
            lastMessage: messages.length > 0 ? messages[messages.length - 1]?.timestamp : undefined
        };
        return stats;
    }
    cleanupOldSessions(maxAge = 30 * 24 * 60 * 60 * 1000) {
        const cutoffDate = new Date(Date.now() - maxAge);
        let deletedCount = 0;
        this.sessions.forEach((session, id) => {
            if (session.updatedAt < cutoffDate) {
                this.sessions.delete(id);
                deletedCount++;
            }
        });
        if (deletedCount > 0) {
            this.saveSessions();
        }
        return deletedCount;
    }
    ensureCurrentSession() {
        let session = this.getCurrentSession();
        if (!session) {
            session = this.createSession();
        }
        return session;
    }
}
//# sourceMappingURL=session-manager.js.map