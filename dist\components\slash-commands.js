import chalk from 'chalk';
export class SlashCommandManager {
    commands = new Map();
    configManager;
    sessionManager;
    llmManager;
    constructor(configManager, sessionManager, llmManager) {
        this.configManager = configManager;
        this.sessionManager = sessionManager;
        this.llmManager = llmManager;
        this.registerDefaultCommands();
    }
    registerDefaultCommands() {
        // Model command
        this.registerCommand({
            name: 'model',
            description: 'Switch or view current AI model',
            aliases: ['m'],
            usage: '/model [model-name] - Switch to a model or list available models',
            handler: async (args) => {
                if (args.length === 0) {
                    await this.showCurrentModel();
                }
                else if (args[0] === 'list') {
                    await this.listAvailableModels();
                }
                else {
                    await this.switchModel(args[0]);
                }
            }
        });
        // Provider command
        this.registerCommand({
            name: 'provider',
            description: 'Switch or view current AI provider',
            aliases: ['p'],
            usage: '/provider [provider-name] - Switch provider or show current',
            handler: async (args) => {
                if (args.length === 0) {
                    await this.showCurrentProvider();
                }
                else {
                    await this.switchProvider(args[0]);
                }
            }
        });
        // Session command
        this.registerCommand({
            name: 'session',
            description: 'Manage chat sessions',
            aliases: ['s'],
            usage: '/session [new|list|switch|delete|export] [name/id]',
            handler: async (args) => {
                const action = args[0];
                switch (action) {
                    case 'new':
                        await this.createNewSession(args.slice(1).join(' '));
                        break;
                    case 'list':
                        await this.listSessions();
                        break;
                    case 'switch':
                        await this.switchSession(args[1]);
                        break;
                    case 'delete':
                        await this.deleteSession(args[1]);
                        break;
                    case 'export':
                        await this.exportSession(args[1]);
                        break;
                    case 'clear':
                        await this.clearCurrentSession();
                        break;
                    default:
                        await this.showCurrentSession();
                }
            }
        });
        // History command
        this.registerCommand({
            name: 'history',
            description: 'View message history',
            aliases: ['h'],
            usage: '/history [limit] - Show recent messages',
            handler: async (args) => {
                const limit = args[0] ? parseInt(args[0]) : undefined;
                await this.showMessageHistory(limit);
            }
        });
        // Config command
        this.registerCommand({
            name: 'config',
            description: 'View or modify configuration',
            aliases: ['c'],
            usage: '/config [key] [value] - View or set configuration',
            handler: async (args) => {
                if (args.length === 0) {
                    await this.showConfig();
                }
                else if (args.length === 1) {
                    await this.showConfigValue(args[0]);
                }
                else {
                    await this.setConfigValue(args[0], args.slice(1).join(' '));
                }
            }
        });
        // Help command
        this.registerCommand({
            name: 'help',
            description: 'Show available commands',
            aliases: ['?'],
            usage: '/help [command] - Show help for all commands or specific command',
            handler: async (args) => {
                if (args.length === 0) {
                    await this.showHelp();
                }
                else {
                    await this.showCommandHelp(args[0]);
                }
            }
        });
        // Clear command
        this.registerCommand({
            name: 'clear',
            description: 'Clear the terminal screen',
            aliases: ['cls'],
            usage: '/clear - Clear the terminal screen',
            handler: async () => {
                console.clear();
                console.log(chalk.cyan('Terminal cleared.'));
            }
        });
        // Status command
        this.registerCommand({
            name: 'status',
            description: 'Show system status',
            aliases: ['st'],
            usage: '/status - Show current system status',
            handler: async () => {
                await this.showStatus();
            }
        });
        // Working directory command
        this.registerCommand({
            name: 'cd',
            description: 'Change working directory',
            aliases: ['dir'],
            usage: '/cd [path] - Change or show working directory',
            handler: async (args) => {
                if (args.length === 0) {
                    await this.showWorkingDirectory();
                }
                else {
                    await this.changeWorkingDirectory(args[0]);
                }
            }
        });
    }
    registerCommand(command) {
        this.commands.set(command.name, command);
        command.aliases.forEach(alias => {
            this.commands.set(alias, command);
        });
    }
    async executeCommand(input) {
        if (!input.startsWith('/')) {
            return false;
        }
        const parts = input.slice(1).split(' ');
        const commandName = parts[0].toLowerCase();
        const args = parts.slice(1);
        const command = this.commands.get(commandName);
        if (!command) {
            console.log(chalk.red(`Unknown command: ${commandName}`));
            console.log(chalk.gray('Type /help to see available commands.'));
            return true;
        }
        try {
            await command.handler(args);
        }
        catch (error) {
            console.log(chalk.red(`Error executing command: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
        return true;
    }
    getCommands() {
        const uniqueCommands = new Map();
        this.commands.forEach(command => {
            uniqueCommands.set(command.name, command);
        });
        return Array.from(uniqueCommands.values());
    }
    // Command implementations
    async showCurrentModel() {
        const model = this.llmManager.getCurrentModel();
        const provider = this.llmManager.getCurrentProvider();
        console.log(chalk.blue(`Current model: ${chalk.bold(model)} (${provider})`));
    }
    async listAvailableModels() {
        try {
            const models = await this.llmManager.getAvailableModels();
            const currentModel = this.llmManager.getCurrentModel();
            console.log(chalk.blue('Available models:'));
            models.forEach(model => {
                const indicator = model === currentModel ? chalk.green('● ') : '  ';
                console.log(`${indicator}${model}`);
            });
        }
        catch (error) {
            console.log(chalk.red(`Failed to fetch models: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async switchModel(modelName) {
        try {
            await this.llmManager.switchModel(modelName);
            console.log(chalk.green(`Switched to model: ${modelName}`));
        }
        catch (error) {
            console.log(chalk.red(`Failed to switch model: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async showCurrentProvider() {
        const provider = this.llmManager.getCurrentProvider();
        console.log(chalk.blue(`Current provider: ${chalk.bold(provider)}`));
    }
    async switchProvider(providerName) {
        try {
            await this.llmManager.switchProvider(providerName);
            console.log(chalk.green(`Switched to provider: ${providerName}`));
        }
        catch (error) {
            console.log(chalk.red(`Failed to switch provider: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async createNewSession(name) {
        const session = this.sessionManager.createSession(name);
        console.log(chalk.green(`Created new session: ${session.name} (${session.id})`));
    }
    async listSessions() {
        const sessions = this.sessionManager.getAllSessions();
        const currentSession = this.sessionManager.getCurrentSession();
        console.log(chalk.blue('Sessions:'));
        sessions.forEach(session => {
            const indicator = session.id === currentSession?.id ? chalk.green('● ') : '  ';
            const messageCount = session.messages.length;
            console.log(`${indicator}${session.name} (${session.id.slice(0, 8)}) - ${messageCount} messages`);
        });
    }
    async switchSession(sessionId) {
        try {
            const session = this.sessionManager.setCurrentSession(sessionId);
            console.log(chalk.green(`Switched to session: ${session.name}`));
        }
        catch (error) {
            console.log(chalk.red(`Failed to switch session: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async deleteSession(sessionId) {
        const success = this.sessionManager.deleteSession(sessionId);
        if (success) {
            console.log(chalk.green(`Deleted session: ${sessionId}`));
        }
        else {
            console.log(chalk.red(`Session not found: ${sessionId}`));
        }
    }
    async exportSession(sessionId) {
        try {
            const targetId = sessionId || this.sessionManager.getCurrentSession()?.id;
            if (!targetId) {
                console.log(chalk.red('No session to export'));
                return;
            }
            const exportData = this.sessionManager.exportSession(targetId);
            console.log(chalk.blue('Session export:'));
            console.log(exportData);
        }
        catch (error) {
            console.log(chalk.red(`Failed to export session: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async clearCurrentSession() {
        this.sessionManager.clearMessages();
        console.log(chalk.green('Current session cleared'));
    }
    async showCurrentSession() {
        const session = this.sessionManager.getCurrentSession();
        if (session) {
            const stats = this.sessionManager.getSessionStats();
            console.log(chalk.blue(`Current session: ${session.name}`));
            console.log(chalk.gray(`  ID: ${session.id}`));
            console.log(chalk.gray(`  Messages: ${stats.messageCount}`));
            console.log(chalk.gray(`  Created: ${session.createdAt.toLocaleString()}`));
        }
        else {
            console.log(chalk.yellow('No active session'));
        }
    }
    async showMessageHistory(limit) {
        const messages = this.sessionManager.getMessageHistory(limit);
        console.log(chalk.blue(`Message history (${messages.length} messages):`));
        messages.forEach((message, index) => {
            const time = message.timestamp.toLocaleTimeString();
            const roleColor = message.role === 'user' ? 'cyan' : 'green';
            console.log(chalk[roleColor](`[${time}] ${message.role}: ${message.content.slice(0, 100)}${message.content.length > 100 ? '...' : ''}`));
        });
    }
    async showConfig() {
        const config = this.configManager.getAll();
        console.log(chalk.blue('Current configuration:'));
        Object.entries(config).forEach(([key, value]) => {
            const displayValue = key === 'apiKey' && value ? '***hidden***' : value;
            console.log(chalk.gray(`  ${key}: ${displayValue}`));
        });
    }
    async showConfigValue(key) {
        if (this.configManager.has(key)) {
            const value = this.configManager.get(key);
            const displayValue = key === 'apiKey' && value ? '***hidden***' : value;
            console.log(chalk.blue(`${key}: ${displayValue}`));
        }
        else {
            console.log(chalk.red(`Configuration key not found: ${key}`));
        }
    }
    async setConfigValue(key, value) {
        try {
            // Parse value based on type
            let parsedValue = value;
            if (value === 'true')
                parsedValue = true;
            else if (value === 'false')
                parsedValue = false;
            else if (!isNaN(Number(value)))
                parsedValue = Number(value);
            this.configManager.set(key, parsedValue);
            console.log(chalk.green(`Set ${key} = ${value}`));
        }
        catch (error) {
            console.log(chalk.red(`Failed to set config: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async showHelp() {
        console.log(chalk.blue.bold('Available Commands:\n'));
        const commands = this.getCommands();
        commands.forEach(command => {
            console.log(chalk.cyan(`/${command.name}`) + chalk.gray(` (${command.aliases.join(', ')})`));
            console.log(chalk.white(`  ${command.description}`));
            console.log(chalk.gray(`  Usage: ${command.usage}\n`));
        });
    }
    async showCommandHelp(commandName) {
        const command = this.commands.get(commandName);
        if (command) {
            console.log(chalk.cyan.bold(`Command: /${command.name}\n`));
            console.log(chalk.white(`Description: ${command.description}`));
            console.log(chalk.gray(`Aliases: ${command.aliases.join(', ')}`));
            console.log(chalk.gray(`Usage: ${command.usage}`));
        }
        else {
            console.log(chalk.red(`Command not found: ${commandName}`));
        }
    }
    async showStatus() {
        const config = this.configManager.getAll();
        const session = this.sessionManager.getCurrentSession();
        const isConfigured = this.configManager.isConfigured();
        console.log(chalk.blue.bold('System Status:\n'));
        console.log(chalk.green(`✓ Configuration: ${isConfigured ? 'Valid' : 'Invalid'}`));
        console.log(chalk.green(`✓ Provider: ${config.provider}`));
        console.log(chalk.green(`✓ Model: ${config.model}`));
        console.log(chalk.green(`✓ Session: ${session ? session.name : 'None'}`));
        console.log(chalk.green(`✓ Working Directory: ${config.workingDirectory}`));
        if (session) {
            const stats = this.sessionManager.getSessionStats();
            console.log(chalk.gray(`  Messages in session: ${stats.messageCount}`));
        }
    }
    async showWorkingDirectory() {
        const config = this.configManager.getAll();
        console.log(chalk.blue(`Working directory: ${config.workingDirectory}`));
    }
    async changeWorkingDirectory(path) {
        try {
            // Resolve path
            const resolvedPath = path.startsWith('/') || path.includes(':') ? path : require('path').resolve(process.cwd(), path);
            // Validate directory exists
            const fs = require('fs');
            if (!fs.existsSync(resolvedPath) || !fs.statSync(resolvedPath).isDirectory()) {
                console.log(chalk.red(`Directory does not exist: ${resolvedPath}`));
                return;
            }
            this.configManager.set('workingDirectory', resolvedPath);
            console.log(chalk.green(`Changed working directory to: ${resolvedPath}`));
        }
        catch (error) {
            console.log(chalk.red(`Failed to change directory: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
}
//# sourceMappingURL=slash-commands.js.map