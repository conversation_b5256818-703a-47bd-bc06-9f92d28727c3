import { LLMMessage, ToolDefinition } from '@/types';
export interface DeepseekConfig {
    apiKey: string;
    model: string;
    baseUrl?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
}
export interface DeepseekResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string | null;
            tool_calls?: Array<{
                id: string;
                type: 'function';
                function: {
                    name: string;
                    arguments: string;
                };
            }>;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}
export declare class DeepseekProvider {
    private client;
    private config;
    constructor(config: DeepseekConfig);
    generateResponse(messages: LLMMessage[], tools?: ToolDefinition[], options?: {
        maxTokens?: number;
        temperature?: number;
        stream?: boolean;
    }): Promise<LLMMessage>;
    validateConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    private formatMessages;
    private formatTools;
    updateConfig(newConfig: Partial<DeepseekConfig>): void;
    getConfig(): DeepseekConfig;
}
//# sourceMappingURL=deepseek.d.ts.map