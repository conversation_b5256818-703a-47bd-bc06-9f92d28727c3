{"version": 3, "file": "spinner.d.ts", "sourceRoot": "", "sources": ["../../src/components/spinner.ts"], "names": [], "mappings": "AAAA,OAAY,EAAO,OAAO,EAAE,MAAM,KAAK,CAAC;AAGxC,MAAM,WAAW,cAAe,SAAQ,OAAO,CAAC,OAAO,CAAC;CAEvD;AAED,qBAAa,aAAa;IACxB,OAAO,CAAC,OAAO,CAAM;IACrB,OAAO,CAAC,MAAM,CAAC,cAAc,CAAiC;gBAElD,OAAO,GAAE,cAAmB;IAkBjC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa;IAQnC,IAAI,IAAI,aAAa;IAKrB,OAAO,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa;IASrC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa;IASlC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa;IASlC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa;IASlC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,aAAa;IAKpC,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,aAAa;IAOvD,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,aAAa;IAK9C,IAAW,UAAU,IAAI,OAAO,CAE/B;IAED,IAAW,IAAI,IAAI,MAAM,CAExB;WAEa,OAAO,IAAI,IAAI;WASf,uBAAuB,CAAC,IAAI,GAAE,MAAwB,GAAG,aAAa;WAQtE,oBAAoB,CAAC,IAAI,GAAE,MAAqB,GAAG,aAAa;WAQhE,qBAAqB,CAAC,IAAI,GAAE,MAA4B,GAAG,aAAa;WAQxE,sBAAsB,CAAC,IAAI,GAAE,MAA+B,GAAG,aAAa;WAQ5E,uBAAuB,CAAC,IAAI,GAAE,MAAwB,GAAG,aAAa;WAQtE,0BAA0B,CAAC,IAAI,GAAE,MAA4B,GAAG,aAAa;WAQ7E,kBAAkB,CAAC,IAAI,GAAE,MAAsB,GAAG,aAAa;WASzD,WAAW,CAAC,CAAC,EAC/B,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EACnB,OAAO,GAAE;QACP,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,OAAO,CAAC,EAAE,aAAa,CAAC;KACpB,GACL,OAAO,CAAC,CAAC,CAAC;WAcO,mBAAmB,CAAC,CAAC,EACvC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EACnB,gBAAgB,EAAE,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,KAAK,IAAI,EAC9D,OAAO,GAAE;QACP,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACd,GACL,OAAO,CAAC,CAAC,CAAC;IAuBb,gBAAuB,eAAe;;;;;;;;;;;;;;;;;;;;;MAqBpC;WAEY,mBAAmB,CAC/B,WAAW,EAAE,MAAM,OAAO,aAAa,CAAC,eAAe,EACvD,IAAI,GAAE,MAAwB,EAC9B,KAAK,GAAE,cAAc,CAAC,OAAO,CAAU,GACtC,aAAa;CAQjB"}