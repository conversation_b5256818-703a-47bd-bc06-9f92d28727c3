import { EventEmitter } from 'events';
export interface RetryOptions {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    jitter?: boolean;
    retryCondition?: (error: Error, attempt: number) => boolean;
    onRetry?: (error: Error, attempt: number) => void;
    timeout?: number;
}
export interface RetryContext {
    attempt: number;
    totalAttempts: number;
    lastError: Error | null;
    startTime: Date;
    delays: number[];
    isRetryable: boolean;
}
export declare class RetryLogic extends EventEmitter {
    private defaultOptions;
    constructor();
    /**
     * Execute function with retry logic
     */
    executeWithRetry<T>(fn: () => Promise<T>, options?: RetryOptions): Promise<T>;
    /**
     * Execute function with timeout
     */
    private executeWithTimeout;
    /**
     * Calculate delay with exponential backoff and jitter
     */
    private calculateDelay;
    /**
     * Determine if error should trigger a retry
     */
    private shouldRetry;
    /**
     * Check if error is retryable by default
     */
    private isRetryableError;
    /**
     * Simple delay function
     */
    private delay;
    /**
     * Create retry strategy for specific scenarios
     */
    static createNetworkRetryStrategy(): RetryOptions;
    /**
     * Create retry strategy for rate limiting
     */
    static createRateLimitRetryStrategy(): RetryOptions;
    /**
     * Create retry strategy for LLM provider errors
     */
    static createLLMProviderRetryStrategy(): RetryOptions;
    /**
     * Create retry strategy for shell commands
     */
    static createShellCommandRetryStrategy(): RetryOptions;
    /**
     * Retry with exponential backoff (utility function)
     */
    static withExponentialBackoff<T>(fn: () => Promise<T>, maxAttempts?: number, baseDelay?: number): Promise<T>;
    /**
     * Retry with linear backoff (utility function)
     */
    static withLinearBackoff<T>(fn: () => Promise<T>, maxAttempts?: number, delay?: number): Promise<T>;
    /**
     * Retry with custom condition (utility function)
     */
    static withCustomCondition<T>(fn: () => Promise<T>, retryCondition: (error: Error, attempt: number) => boolean, maxAttempts?: number): Promise<T>;
    /**
     * Format retry context for logging
     */
    static formatRetryContext(context: RetryContext): string;
}
//# sourceMappingURL=retry-logic.d.ts.map