{"version": 3, "file": "tool-manager.js", "sourceRoot": "", "sources": ["../../src/core/tool-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAoD,YAAY,EAAE,MAAM,SAAS,CAAC;AACzF,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC,MAAM,OAAO,WAAW;IACd,KAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;IACpC,SAAS,CAAY;IAE7B,YAAY,gBAAyB;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACjD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,YAAY,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7D,CAAC;IAEM,YAAY,CAAC,IAAY,EAAE,IAAS;QACzC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEM,kBAAkB;QACvB,MAAM,WAAW,GAAqB,EAAE,CAAC;QAEzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YAChC,IAAI,OAAO,IAAI,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAClC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAkB;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEtC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,MAAM,EAAE,gBAAgB,QAAQ,aAAa;gBAC7C,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS,QAAQ,qBAAqB;gBAC7C,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAS,CAAC;YACd,IAAI,CAAC;gBACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,MAAM,EAAE,2CAA2C,QAAQ,GAAG;oBAC9D,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8BAA8B,UAAU,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;oBACzG,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC,CAAC;YACJ,CAAC;YAED,IAAI,MAAW,CAAC;YAEhB,8BAA8B;YAC9B,IAAI,QAAQ,KAAK,uBAAuB,EAAE,CAAC;gBACzC,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC;iBAAM,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC9C,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,SAAS,QAAQ,mCAAmC,CAAC,CAAC;YACxE,CAAC;YAED,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACrC,OAAO,EAAE,IAAI;gBACb,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,MAAM,EAAE,yBAAyB,QAAQ,MAAM,YAAY,EAAE;gBAC7D,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;gBACnB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAS;QACzC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;QAErE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAChD,CAAC;QAED,4BAA4B;QAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE;YACnD,gBAAgB;YAChB,OAAO;YACP,eAAe;SAChB,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,MAAW;QAClC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,+BAA+B;YAC/B,IAAI,QAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,MAAM,IAAI,UAAU,IAAI,MAAM,EAAE,CAAC;gBACrE,MAAM,MAAM,GAAG,EAAE,CAAC;gBAElB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC3C,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,mBAAmB,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;gBACzD,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;gBAE1C,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC;YAED,yCAAyC;YACzC,IAAI,CAAC;gBACH,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,SAAqB;QACjD,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,4EAA4E;QAC5E,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,wBAAwB,CAAC,WAAyB;QACvD,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChC,EAAE,EAAE,MAAM,EAAE;YACZ,IAAI,EAAE,MAAe;YACrB,OAAO,EAAE,MAAM,CAAC,MAAM;YACtB,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,mBAAmB,CAAC,SAAiB;QAC1C,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;IAC9C,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,SAAiB;QACrD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC9B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;IACpD,CAAC;IAEM,iBAAiB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACvC,CAAC;IAEM,YAAY,CAAC,OAAmB;QACrC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEM,gBAAgB,CAAC,OAAmB;QACzC,OAAO,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAClC,OAAmB,EACnB,eAAkE;QAElE,MAAM,cAAc,GAAiB,EAAE,CAAC;QAExC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEjD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAEpD,gCAAgC;gBAChC,IAAI,eAAe,EAAE,CAAC;oBACpB,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACpC,CAAC;gBAED,6BAA6B;gBAC7B,MAAM,iBAAiB,GAAe;oBACpC,EAAE,EAAE,MAAM,EAAE;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;oBACtB,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEM,iBAAiB;QAKtB,uEAAuE;QACvE,OAAO;YACL,eAAe,EAAE,CAAC;YAClB,SAAS,EAAE,EAAE;YACb,oBAAoB,EAAE,CAAC;SACxB,CAAC;IACJ,CAAC;CACF"}