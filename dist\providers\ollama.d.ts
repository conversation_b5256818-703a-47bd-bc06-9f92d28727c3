import { LLMMessage, ToolDefinition } from '@/types';
export interface OllamaConfig {
    baseUrl: string;
    model: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
}
export interface OllamaResponse {
    model: string;
    created_at: string;
    message: {
        role: string;
        content: string;
        tool_calls?: Array<{
            function: {
                name: string;
                arguments: Record<string, any>;
            };
        }>;
    };
    done: boolean;
    total_duration?: number;
    load_duration?: number;
    prompt_eval_count?: number;
    prompt_eval_duration?: number;
    eval_count?: number;
    eval_duration?: number;
}
export interface OllamaModel {
    name: string;
    model: string;
    modified_at: string;
    size: number;
    digest: string;
    details: {
        parent_model: string;
        format: string;
        family: string;
        families: string[];
        parameter_size: string;
        quantization_level: string;
    };
}
export declare class OllamaProvider {
    private client;
    private config;
    constructor(config: OllamaConfig);
    generateResponse(messages: LLMMessage[], tools?: ToolDefinition[], options?: {
        maxTokens?: number;
        temperature?: number;
        stream?: boolean;
    }): Promise<LLMMessage>;
    validateConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    pullModel(modelName: string): Promise<void>;
    deleteModel(modelName: string): Promise<void>;
    getModelInfo(modelName: string): Promise<any>;
    private formatMessages;
    private formatTools;
    updateConfig(newConfig: Partial<OllamaConfig>): void;
    getConfig(): OllamaConfig;
}
//# sourceMappingURL=ollama.d.ts.map