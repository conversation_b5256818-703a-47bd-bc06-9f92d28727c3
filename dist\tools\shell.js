import { execa } from 'execa';
import { CommandError } from '@/types';
import { platform } from 'os';
export class ShellTool {
    workingDirectory;
    maxExecutionTime = 300000; // 5 minutes
    dangerousCommands = [
        'rm -rf /',
        'del /f /s /q C:\\',
        'format',
        'fdisk',
        'mkfs',
        'dd if=/dev/zero',
        'shutdown',
        'reboot',
        'halt',
        'poweroff',
        'init 0',
        'init 6',
        'systemctl poweroff',
        'systemctl reboot'
    ];
    constructor(workingDirectory = process.cwd()) {
        this.workingDirectory = workingDirectory;
    }
    getToolDefinition() {
        return {
            name: 'execute_shell_command',
            description: `Execute shell commands and capture their output. This tool allows running system commands, 
      file operations, process management, and system interactions. It captures both stdout and stderr, 
      tracks execution time, and provides detailed error information.
      
      IMPORTANT SAFETY GUIDELINES:
      - Never execute destructive commands without explicit user approval
      - Always validate commands before execution
      - Be cautious with file system operations
      - Avoid commands that could harm the system or data
      - Use appropriate error handling and timeouts
      
      WHEN TO USE:
      - File and directory operations (ls, mkdir, cp, mv, etc.)
      - Process management (ps, kill, etc.)
      - System information gathering (df, free, uname, etc.)
      - Package management (npm, pip, apt, etc.)
      - Git operations (git status, git add, git commit, etc.)
      - Build and compilation tasks
      - Testing and validation commands
      - Network operations (ping, curl, wget, etc.)
      
      WHEN NOT TO USE:
      - Destructive operations without user confirmation
      - Commands that require interactive input
      - Long-running services (use background execution instead)
      - Commands that could compromise system security
      
      PARALLEL vs SEQUENTIAL EXECUTION:
      - Use PARALLEL for independent operations (multiple file checks, parallel builds)
      - Use SEQUENTIAL for dependent operations (cd then ls, compile then run)
      - Use SEQUENTIAL for commands that modify the same resources
      
      EXAMPLES:
      - File operations: "ls -la", "mkdir project", "cp file.txt backup.txt"
      - System info: "df -h", "free -m", "ps aux"
      - Development: "npm install", "npm run build", "git status"
      - Network: "ping google.com", "curl -I https://example.com"`,
            parameters: {
                type: 'object',
                properties: {
                    command: {
                        type: 'string',
                        description: 'The shell command to execute. Use proper shell syntax for the target platform.'
                    },
                    workingDirectory: {
                        type: 'string',
                        description: 'Optional working directory for command execution. Defaults to current directory.'
                    },
                    timeout: {
                        type: 'number',
                        description: 'Optional timeout in milliseconds. Defaults to 300000 (5 minutes).'
                    },
                    requireApproval: {
                        type: 'boolean',
                        description: 'Whether to require user approval before executing potentially dangerous commands.'
                    }
                },
                required: ['command']
            },
            usage: {
                when: 'Need to execute system commands, file operations, or interact with the operating system',
                whenNot: 'For destructive operations without approval, interactive commands, or long-running services',
                parallel: true,
                sequential: true,
                examples: [
                    'ls -la # List directory contents with details',
                    'mkdir -p project/src # Create directory structure',
                    'npm install # Install dependencies',
                    'git status # Check git repository status',
                    'ps aux | grep node # Find Node.js processes',
                    'df -h # Check disk usage',
                    'curl -I https://api.example.com # Check API endpoint'
                ]
            }
        };
    }
    async execute(command, options = {}) {
        const startTime = Date.now();
        const cwd = options.workingDirectory || this.workingDirectory;
        const timeout = options.timeout || this.maxExecutionTime;
        try {
            // Validate command safety
            this.validateCommand(command);
            // Parse command for cross-platform compatibility
            const { file, args } = this.parseCommand(command);
            // Execute command
            const result = await execa(file, args, {
                cwd,
                timeout,
                reject: false,
                stripFinalNewline: false,
                encoding: 'utf8',
                env: { ...process.env, FORCE_COLOR: '0' } // Disable colors for cleaner output
            });
            const executionTime = Date.now() - startTime;
            return {
                stdout: result.stdout || '',
                stderr: result.stderr || '',
                exitCode: result.exitCode || 0,
                command,
                executionTime,
                success: result.exitCode === 0
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            if (error.name === 'TimeoutError') {
                throw new CommandError(`Command timed out after ${timeout}ms: ${command}`, { command, timeout, executionTime });
            }
            return {
                stdout: error.stdout || '',
                stderr: error.stderr || error.message || 'Unknown error',
                exitCode: error.exitCode || 1,
                command,
                executionTime,
                success: false
            };
        }
    }
    validateCommand(command) {
        const normalizedCommand = command.toLowerCase().trim();
        // Check for dangerous commands
        for (const dangerous of this.dangerousCommands) {
            if (normalizedCommand.includes(dangerous.toLowerCase())) {
                throw new CommandError(`Potentially dangerous command detected: ${command}. This command could harm your system.`, { command, reason: 'dangerous_command' });
            }
        }
        // Check for empty command
        if (!command.trim()) {
            throw new CommandError('Empty command provided');
        }
    }
    parseCommand(command) {
        const isWindows = platform() === 'win32';
        if (isWindows) {
            // On Windows, use cmd.exe to execute commands
            return {
                file: 'cmd.exe',
                args: ['/c', command]
            };
        }
        else {
            // On Unix-like systems, use sh
            return {
                file: 'sh',
                args: ['-c', command]
            };
        }
    }
    setWorkingDirectory(directory) {
        this.workingDirectory = directory;
    }
    getWorkingDirectory() {
        return this.workingDirectory;
    }
    async validateDirectory(directory) {
        try {
            const result = await this.execute(`test -d "${directory}"`, { timeout: 5000 });
            return result.success;
        }
        catch {
            return false;
        }
    }
    async getCurrentDirectory() {
        try {
            const result = await this.execute('pwd', { timeout: 5000 });
            return result.success ? result.stdout.trim() : this.workingDirectory;
        }
        catch {
            return this.workingDirectory;
        }
    }
}
//# sourceMappingURL=shell.js.map