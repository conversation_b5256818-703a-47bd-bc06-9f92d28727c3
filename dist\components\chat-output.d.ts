import { EventEmitter } from 'events';
import { LLMMessage, Tool<PERSON>all, ToolR<PERSON>ult } from '@/types';
export interface OutputFormatOptions {
    showTimestamps?: boolean;
    showRoleColors?: boolean;
    maxLineLength?: number;
    codeHighlighting?: boolean;
    compactMode?: boolean;
    showTokenCount?: boolean;
}
export interface FormattedMessage {
    id: string;
    content: string;
    rawContent: string;
    role: string;
    timestamp: Date;
    metadata?: {
        tokenCount?: number;
        executionTime?: number;
        toolCalls?: ToolCall[];
    };
}
export declare class ChatOutputProcessor extends EventEmitter {
    private options;
    private messageBuffer;
    private maxBufferSize;
    constructor(options?: OutputFormatOptions);
    /**
     * Format a message for display
     */
    formatMessage(message: LLMMessage, metadata?: any): FormattedMessage;
    /**
     * Process message content with formatting
     */
    private processMessageContent;
    /**
     * Format role indicator
     */
    private formatRole;
    /**
     * Format user message
     */
    private formatUserMessage;
    /**
     * Format assistant message
     */
    private formatAssistantMessage;
    /**
     * Format tool message
     */
    private formatToolMessage;
    /**
     * Format system message
     */
    private formatSystemMessage;
    /**
     * Format tool calls
     */
    private formatToolCalls;
    /**
     * Format tool arguments
     */
    private formatToolArguments;
    /**
     * Format code blocks
     */
    private formatCodeBlocks;
    /**
     * Format lists
     */
    private formatLists;
    /**
     * Format emphasis
     */
    private formatEmphasis;
    /**
     * Format tool result
     */
    formatToolResult(result: ToolResult): string;
    /**
     * Format streaming response
     */
    formatStreamingChunk(chunk: string, isComplete?: boolean): string;
    /**
     * Add message to buffer
     */
    private addToBuffer;
    /**
     * Get message buffer
     */
    getMessageBuffer(): FormattedMessage[];
    /**
     * Clear message buffer
     */
    clearBuffer(): void;
    /**
     * Export messages as plain text
     */
    exportAsText(includeTimestamps?: boolean): string;
    /**
     * Export messages as JSON
     */
    exportAsJSON(): string;
    /**
     * Search messages
     */
    searchMessages(query: string, caseSensitive?: boolean): FormattedMessage[];
    /**
     * Get message statistics
     */
    getMessageStats(): {
        totalMessages: number;
        messagesByRole: Record<string, number>;
        averageLength: number;
        totalTokens?: number;
    };
    /**
     * Update formatting options
     */
    updateOptions(options: Partial<OutputFormatOptions>): void;
}
//# sourceMappingURL=chat-output.d.ts.map