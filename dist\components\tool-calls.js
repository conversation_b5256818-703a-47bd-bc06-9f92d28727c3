import { EventEmitter } from 'events';
import { ModernSpinner } from './spinner';
import chalk from 'chalk';
import { nanoid } from 'nanoid';
export class ToolCallsProcessor extends EventEmitter {
    executionContexts = new Map();
    toolDefinitions = new Map();
    defaultOptions;
    constructor(options = {}) {
        super();
        this.defaultOptions = {
            parallel: false,
            maxConcurrency: 3,
            timeout: 300000, // 5 minutes
            retryAttempts: 2,
            confirmBeforeExecution: false,
            showProgress: true,
            ...options
        };
    }
    /**
     * Register tool definitions for better processing
     */
    registerToolDefinition(definition) {
        this.toolDefinitions.set(definition.name, definition);
    }
    /**
     * Create execution plan for tool calls
     */
    createExecutionPlan(toolCalls) {
        const plan = {
            id: nanoid(),
            toolCalls,
            executionOrder: [],
            estimatedDuration: 0,
            riskLevel: 'low',
            warnings: []
        };
        // Analyze tool calls for dependencies and risks
        const { order, warnings, riskLevel } = this.analyzeToolCalls(toolCalls);
        plan.executionOrder = order;
        plan.warnings = warnings;
        plan.riskLevel = riskLevel;
        plan.estimatedDuration = this.estimateExecutionTime(toolCalls);
        return plan;
    }
    /**
     * Analyze tool calls for execution order and risks
     */
    analyzeToolCalls(toolCalls) {
        const warnings = [];
        let riskLevel = 'low';
        // Check for dangerous operations
        const dangerousTools = ['execute_shell_command'];
        const hasDangerousTools = toolCalls.some(call => dangerousTools.includes(call.function.name));
        if (hasDangerousTools) {
            riskLevel = 'high';
            warnings.push('⚠️  Dangerous operations detected. Review commands carefully.');
        }
        // Check for file system operations
        const fileSystemOperations = toolCalls.filter(call => {
            try {
                const args = JSON.parse(call.function.arguments);
                const command = args.command || '';
                return /rm|del|mv|cp|mkdir|rmdir/i.test(command);
            }
            catch {
                return false;
            }
        });
        if (fileSystemOperations.length > 0) {
            if (riskLevel === 'low')
                riskLevel = 'medium';
            warnings.push('📁 File system operations detected. Ensure you have backups.');
        }
        // Check for network operations
        const networkOperations = toolCalls.filter(call => {
            try {
                const args = JSON.parse(call.function.arguments);
                const command = args.command || '';
                return /curl|wget|ssh|scp|rsync|ping/i.test(command);
            }
            catch {
                return false;
            }
        });
        if (networkOperations.length > 0) {
            warnings.push('🌐 Network operations detected. Check connectivity and permissions.');
        }
        // Determine execution order
        const order = this.determineExecutionOrder(toolCalls);
        return { order, warnings, riskLevel };
    }
    /**
     * Determine optimal execution order
     */
    determineExecutionOrder(toolCalls) {
        // For now, simple sequential execution
        // TODO: Implement dependency analysis for parallel execution
        if (this.defaultOptions.parallel) {
            // Group by parallelizable operations
            const groups = [];
            let currentGroup = [];
            for (const toolCall of toolCalls) {
                const definition = this.toolDefinitions.get(toolCall.function.name);
                if (definition?.usage.parallel && currentGroup.length < this.defaultOptions.maxConcurrency) {
                    currentGroup.push(toolCall);
                }
                else {
                    if (currentGroup.length > 0) {
                        groups.push([...currentGroup]);
                        currentGroup = [];
                    }
                    currentGroup.push(toolCall);
                }
            }
            if (currentGroup.length > 0) {
                groups.push(currentGroup);
            }
            return groups;
        }
        else {
            // Sequential execution
            return toolCalls.map(call => [call]);
        }
    }
    /**
     * Estimate execution time
     */
    estimateExecutionTime(toolCalls) {
        let totalTime = 0;
        for (const toolCall of toolCalls) {
            const definition = this.toolDefinitions.get(toolCall.function.name);
            // Base estimation on tool type
            switch (toolCall.function.name) {
                case 'execute_shell_command':
                    totalTime += 5000; // 5 seconds average
                    break;
                default:
                    totalTime += 2000; // 2 seconds default
            }
        }
        return totalTime;
    }
    /**
     * Execute tool calls according to plan
     */
    async executeToolCalls(toolCalls, executor, options = {}) {
        const execOptions = { ...this.defaultOptions, ...options };
        const plan = this.createExecutionPlan(toolCalls);
        this.emit('execution-plan-created', plan);
        // Show warnings if any
        if (plan.warnings.length > 0) {
            this.emit('execution-warnings', plan.warnings);
        }
        // Request confirmation if needed
        if (execOptions.confirmBeforeExecution) {
            const confirmed = await this.requestConfirmation(plan);
            if (!confirmed) {
                throw new Error('Execution cancelled by user');
            }
        }
        const results = [];
        try {
            this.emit('execution-started', plan);
            for (const group of plan.executionOrder) {
                if (group.length === 1) {
                    // Sequential execution
                    const result = await this.executeSingleTool(group[0], executor, execOptions);
                    results.push(result);
                }
                else {
                    // Parallel execution
                    const groupResults = await this.executeToolGroup(group, executor, execOptions);
                    results.push(...groupResults);
                }
            }
            this.emit('execution-completed', { plan, results });
            return results;
        }
        catch (error) {
            this.emit('execution-failed', { plan, error, results });
            throw error;
        }
    }
    /**
     * Execute a single tool with full context tracking
     */
    async executeSingleTool(toolCall, executor, options) {
        const context = {
            id: nanoid(),
            toolCall,
            startTime: new Date(),
            status: 'pending',
            retryCount: 0
        };
        this.executionContexts.set(context.id, context);
        try {
            context.status = 'running';
            this.emit('tool-execution-started', context);
            // Show progress spinner
            if (options.showProgress) {
                const toolName = chalk.cyan(toolCall.function.name);
                context.spinner = ModernSpinner.createToolExecutionSpinner(`Executing ${toolName}...`);
                context.spinner.start();
            }
            // Execute with timeout and retry logic
            const result = await this.executeWithRetry(toolCall, executor, options, context);
            context.status = 'completed';
            context.result = result;
            context.endTime = new Date();
            if (context.spinner) {
                if (result.success) {
                    context.spinner.succeed(`${chalk.cyan(toolCall.function.name)} completed`);
                }
                else {
                    context.spinner.fail(`${chalk.cyan(toolCall.function.name)} failed`);
                }
            }
            this.emit('tool-execution-completed', context);
            return result;
        }
        catch (error) {
            context.status = 'failed';
            context.error = error;
            context.endTime = new Date();
            if (context.spinner) {
                context.spinner.fail(`${chalk.cyan(toolCall.function.name)} failed: ${error}`);
            }
            this.emit('tool-execution-failed', context);
            throw error;
        }
        finally {
            this.executionContexts.delete(context.id);
        }
    }
    /**
     * Execute tool group in parallel
     */
    async executeToolGroup(toolCalls, executor, options) {
        const promises = toolCalls.map(toolCall => this.executeSingleTool(toolCall, executor, options));
        return Promise.all(promises);
    }
    /**
     * Execute with retry logic
     */
    async executeWithRetry(toolCall, executor, options, context) {
        let lastError = null;
        for (let attempt = 0; attempt <= options.retryAttempts; attempt++) {
            try {
                context.retryCount = attempt;
                if (attempt > 0) {
                    this.emit('tool-retry-attempt', { context, attempt });
                    if (context.spinner) {
                        context.spinner.setText(`Retrying ${chalk.cyan(toolCall.function.name)}... (Attempt ${attempt + 1})`);
                    }
                    // Exponential backoff
                    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                // Execute with timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Tool execution timeout')), options.timeout);
                });
                const result = await Promise.race([
                    executor(toolCall),
                    timeoutPromise
                ]);
                return result;
            }
            catch (error) {
                lastError = error;
                // Check if error is retryable
                if (!this.isRetryableError(error) || attempt === options.retryAttempts) {
                    throw error;
                }
            }
        }
        throw lastError || new Error('Unknown execution error');
    }
    /**
     * Check if error is retryable
     */
    isRetryableError(error) {
        const retryablePatterns = [
            /timeout/i,
            /network/i,
            /connection/i,
            /temporary/i,
            /rate limit/i
        ];
        return retryablePatterns.some(pattern => pattern.test(error.message));
    }
    /**
     * Request user confirmation
     */
    async requestConfirmation(plan) {
        return new Promise((resolve) => {
            this.emit('confirmation-required', {
                plan,
                callback: (confirmed) => resolve(confirmed)
            });
        });
    }
    /**
     * Format tool call for display
     */
    formatToolCall(toolCall) {
        const functionName = chalk.cyan.bold(toolCall.function.name);
        try {
            const args = JSON.parse(toolCall.function.arguments);
            const formattedArgs = Object.entries(args)
                .map(([key, value]) => `${chalk.yellow(key)}: ${chalk.white(JSON.stringify(value))}`)
                .join(', ');
            return `🔧 ${functionName}(${formattedArgs})`;
        }
        catch {
            return `🔧 ${functionName}(${chalk.gray(toolCall.function.arguments)})`;
        }
    }
    /**
     * Format tool result for display
     */
    formatToolResult(result) {
        const status = result.success ?
            chalk.green('✅ SUCCESS') :
            chalk.red('❌ FAILED');
        const executionTime = chalk.gray(`(${result.executionTime}ms)`);
        const toolId = chalk.gray(`[${result.toolCallId.slice(0, 8)}]`);
        let output = `${status} ${toolId} ${executionTime}\n`;
        if (result.success) {
            output += this.formatSuccessOutput(result.result);
        }
        else {
            output += this.formatErrorOutput(result.error || result.result);
        }
        return output;
    }
    /**
     * Format success output
     */
    formatSuccessOutput(output) {
        const lines = output.split('\n');
        return lines.map(line => {
            if (line.trim() === '')
                return line;
            return chalk.gray('│ ') + line;
        }).join('\n');
    }
    /**
     * Format error output
     */
    formatErrorOutput(error) {
        const lines = error.split('\n');
        return lines.map(line => {
            if (line.trim() === '')
                return line;
            return chalk.red('│ ') + chalk.red(line);
        }).join('\n');
    }
    /**
     * Get execution statistics
     */
    getExecutionStats() {
        // This would be implemented with persistent storage
        // For now, return empty stats
        return {
            totalExecutions: 0,
            successRate: 0,
            averageExecutionTime: 0,
            mostUsedTools: []
        };
    }
    /**
     * Cancel all running executions
     */
    cancelAllExecutions() {
        for (const context of this.executionContexts.values()) {
            if (context.status === 'running') {
                context.status = 'cancelled';
                if (context.spinner) {
                    context.spinner.fail(`${chalk.cyan(context.toolCall.function.name)} cancelled`);
                }
                this.emit('tool-execution-cancelled', context);
            }
        }
        this.executionContexts.clear();
    }
    /**
     * Get active executions
     */
    getActiveExecutions() {
        return Array.from(this.executionContexts.values())
            .filter(context => context.status === 'running');
    }
}
//# sourceMappingURL=tool-calls.js.map