import { EventEmitter } from 'events';
import { format, isToday, isYesterday, startOfDay } from 'date-fns';
import chalk from 'chalk';
export class MessageHistoryManager extends EventEmitter {
    options;
    messageIndex = new Map();
    sessionMessages = new Map();
    searchIndex = new Map(); // word -> message IDs
    constructor(options = {}) {
        super();
        this.options = {
            maxMessages: 10000,
            groupByDate: true,
            showMetadata: true,
            autoSave: true,
            searchEnabled: true,
            ...options
        };
    }
    /**
     * Add message to history
     */
    addMessage(message, sessionId) {
        // Add to message index
        this.messageIndex.set(message.id, message);
        // Add to session messages
        if (!this.sessionMessages.has(sessionId)) {
            this.sessionMessages.set(sessionId, []);
        }
        const sessionMessages = this.sessionMessages.get(sessionId);
        sessionMessages.push(message);
        // Update search index
        if (this.options.searchEnabled) {
            this.updateSearchIndex(message);
        }
        // Enforce message limit
        this.enforceMessageLimit();
        this.emit('message-added', { message, sessionId });
    }
    /**
     * Get messages for a session
     */
    getSessionMessages(sessionId) {
        return this.sessionMessages.get(sessionId) || [];
    }
    /**
     * Get all messages across all sessions
     */
    getAllMessages() {
        const allMessages = [];
        for (const messages of this.sessionMessages.values()) {
            allMessages.push(...messages);
        }
        return allMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }
    /**
     * Get messages grouped by date
     */
    getMessagesByDate(sessionId) {
        const messages = sessionId ?
            this.getSessionMessages(sessionId) :
            this.getAllMessages();
        if (!this.options.groupByDate) {
            return [{
                    date: new Date(),
                    label: 'All Messages',
                    messages,
                    count: messages.length
                }];
        }
        const groups = new Map();
        for (const message of messages) {
            const dateKey = format(startOfDay(message.timestamp), 'yyyy-MM-dd');
            if (!groups.has(dateKey)) {
                const label = this.formatDateLabel(message.timestamp);
                groups.set(dateKey, {
                    date: message.timestamp,
                    label,
                    messages: [],
                    count: 0
                });
            }
            const group = groups.get(dateKey);
            group.messages.push(message);
            group.count++;
        }
        return Array.from(groups.values()).sort((a, b) => b.date.getTime() - a.date.getTime());
    }
    /**
     * Format date label for grouping
     */
    formatDateLabel(date) {
        if (isToday(date)) {
            return 'Today';
        }
        else if (isYesterday(date)) {
            return 'Yesterday';
        }
        else {
            return format(date, 'MMMM d, yyyy');
        }
    }
    /**
     * Search messages
     */
    searchMessages(query, options = {}) {
        if (!this.options.searchEnabled) {
            return [];
        }
        const results = [];
        const searchTerm = options.caseSensitive ? query : query.toLowerCase();
        // Get messages to search
        const messages = options.sessionId ?
            this.getSessionMessages(options.sessionId) :
            this.getAllMessages();
        for (const message of messages) {
            // Apply filters
            if (options.role && message.role !== options.role) {
                continue;
            }
            if (options.dateRange) {
                const messageDate = message.timestamp;
                if (messageDate < options.dateRange.start || messageDate > options.dateRange.end) {
                    continue;
                }
            }
            // Search content
            const content = options.caseSensitive ? message.content : message.content.toLowerCase();
            const isMatch = options.exactMatch ?
                content === searchTerm :
                content.includes(searchTerm);
            if (isMatch) {
                // Find session info
                const sessionId = this.findMessageSession(message.id);
                const sessionName = sessionId ? `Session ${sessionId.slice(0, 8)}` : 'Unknown';
                // Get context messages (2 before, 2 after)
                const context = this.getMessageContext(message.id, sessionId, 2);
                // Extract matched text with context
                const matchedText = this.extractMatchedText(content, searchTerm, 50);
                results.push({
                    message,
                    sessionId: sessionId || '',
                    sessionName,
                    matchedText,
                    context
                });
            }
        }
        return results.sort((a, b) => b.message.timestamp.getTime() - a.message.timestamp.getTime());
    }
    /**
     * Find which session a message belongs to
     */
    findMessageSession(messageId) {
        for (const [sessionId, messages] of this.sessionMessages.entries()) {
            if (messages.some(msg => msg.id === messageId)) {
                return sessionId;
            }
        }
        return null;
    }
    /**
     * Get context messages around a specific message
     */
    getMessageContext(messageId, sessionId, contextSize) {
        if (!sessionId)
            return [];
        const sessionMessages = this.getSessionMessages(sessionId);
        const messageIndex = sessionMessages.findIndex(msg => msg.id === messageId);
        if (messageIndex === -1)
            return [];
        const start = Math.max(0, messageIndex - contextSize);
        const end = Math.min(sessionMessages.length, messageIndex + contextSize + 1);
        return sessionMessages.slice(start, end);
    }
    /**
     * Extract matched text with surrounding context
     */
    extractMatchedText(content, searchTerm, contextLength) {
        const index = content.toLowerCase().indexOf(searchTerm.toLowerCase());
        if (index === -1)
            return content.slice(0, contextLength);
        const start = Math.max(0, index - contextLength / 2);
        const end = Math.min(content.length, index + searchTerm.length + contextLength / 2);
        let excerpt = content.slice(start, end);
        if (start > 0)
            excerpt = '...' + excerpt;
        if (end < content.length)
            excerpt = excerpt + '...';
        return excerpt;
    }
    /**
     * Update search index for a message
     */
    updateSearchIndex(message) {
        const words = message.content.toLowerCase()
            .split(/\W+/)
            .filter(word => word.length > 2);
        for (const word of words) {
            if (!this.searchIndex.has(word)) {
                this.searchIndex.set(word, new Set());
            }
            this.searchIndex.get(word).add(message.id);
        }
    }
    /**
     * Get message statistics
     */
    getStatistics() {
        const allMessages = this.getAllMessages();
        if (allMessages.length === 0) {
            return {
                totalMessages: 0,
                messagesByRole: {},
                messagesBySession: {},
                dateRange: null,
                averageMessageLength: 0,
                mostActiveDay: null
            };
        }
        // Count by role
        const messagesByRole = {};
        let totalLength = 0;
        for (const message of allMessages) {
            messagesByRole[message.role] = (messagesByRole[message.role] || 0) + 1;
            totalLength += message.content.length;
        }
        // Count by session
        const messagesBySession = {};
        for (const [sessionId, messages] of this.sessionMessages.entries()) {
            messagesBySession[sessionId] = messages.length;
        }
        // Date range
        const timestamps = allMessages.map(msg => msg.timestamp.getTime());
        const dateRange = {
            start: new Date(Math.min(...timestamps)),
            end: new Date(Math.max(...timestamps))
        };
        // Most active day
        const dayGroups = this.getMessagesByDate();
        const mostActiveDay = dayGroups.length > 0 ?
            dayGroups.reduce((max, group) => group.count > max.count ? group : max).label :
            null;
        return {
            totalMessages: allMessages.length,
            messagesByRole,
            messagesBySession,
            dateRange,
            averageMessageLength: Math.round(totalLength / allMessages.length),
            mostActiveDay
        };
    }
    /**
     * Export message history
     */
    exportHistory(exportFormat = 'json') {
        const allMessages = this.getAllMessages();
        if (exportFormat === 'text') {
            return allMessages.map(msg => {
                const timestamp = format(msg.timestamp, 'yyyy-MM-dd HH:mm:ss');
                return `[${timestamp}] ${msg.role.toUpperCase()}: ${msg.content}`;
            }).join('\n\n');
        }
        return JSON.stringify({
            exportDate: new Date().toISOString(),
            totalMessages: allMessages.length,
            sessions: Object.fromEntries(this.sessionMessages.entries()),
            statistics: this.getStatistics()
        }, null, 2);
    }
    /**
     * Import message history
     */
    importHistory(data) {
        try {
            const parsed = JSON.parse(data);
            if (parsed.sessions) {
                this.sessionMessages.clear();
                this.messageIndex.clear();
                this.searchIndex.clear();
                for (const [sessionId, messages] of Object.entries(parsed.sessions)) {
                    const sessionMessages = messages.map(msg => ({
                        ...msg,
                        timestamp: new Date(msg.timestamp)
                    }));
                    this.sessionMessages.set(sessionId, sessionMessages);
                    for (const message of sessionMessages) {
                        this.messageIndex.set(message.id, message);
                        if (this.options.searchEnabled) {
                            this.updateSearchIndex(message);
                        }
                    }
                }
                this.emit('history-imported', this.getAllMessages().length);
            }
        }
        catch (error) {
            this.emit('import-error', error);
        }
    }
    /**
     * Clear message history
     */
    clearHistory(sessionId) {
        if (sessionId) {
            // Clear specific session
            const messages = this.sessionMessages.get(sessionId) || [];
            for (const message of messages) {
                this.messageIndex.delete(message.id);
            }
            this.sessionMessages.delete(sessionId);
            this.emit('session-cleared', sessionId);
        }
        else {
            // Clear all history
            this.sessionMessages.clear();
            this.messageIndex.clear();
            this.searchIndex.clear();
            this.emit('history-cleared');
        }
    }
    /**
     * Enforce message limit
     */
    enforceMessageLimit() {
        const totalMessages = this.getAllMessages().length;
        if (totalMessages > this.options.maxMessages) {
            // Remove oldest messages
            const excess = totalMessages - this.options.maxMessages;
            const allMessages = this.getAllMessages();
            const toRemove = allMessages.slice(0, excess);
            for (const message of toRemove) {
                this.messageIndex.delete(message.id);
                // Remove from session
                const sessionId = this.findMessageSession(message.id);
                if (sessionId) {
                    const sessionMessages = this.sessionMessages.get(sessionId);
                    const index = sessionMessages.findIndex(msg => msg.id === message.id);
                    if (index !== -1) {
                        sessionMessages.splice(index, 1);
                    }
                }
            }
            this.emit('messages-pruned', excess);
        }
    }
    /**
     * Get recent messages
     */
    getRecentMessages(count = 50, sessionId) {
        const messages = sessionId ?
            this.getSessionMessages(sessionId) :
            this.getAllMessages();
        return messages.slice(-count);
    }
    /**
     * Format message for display
     */
    formatMessageForDisplay(message, showMetadata = true) {
        const timestamp = chalk.gray(format(message.timestamp, 'HH:mm:ss'));
        const role = this.formatRole(message.role);
        const content = message.content.length > 100 ?
            message.content.slice(0, 100) + '...' :
            message.content;
        let formatted = `${timestamp} ${role} ${content}`;
        if (showMetadata && this.options.showMetadata) {
            const metadata = [];
            if (message.toolCalls?.length) {
                metadata.push(chalk.magenta(`${message.toolCalls.length} tool calls`));
            }
            if (metadata.length > 0) {
                formatted += chalk.gray(` (${metadata.join(', ')})`);
            }
        }
        return formatted;
    }
    /**
     * Format role for display
     */
    formatRole(role) {
        const roleColors = {
            user: chalk.cyan('USER'),
            assistant: chalk.green('AI'),
            tool: chalk.magenta('TOOL'),
            system: chalk.yellow('SYS')
        };
        return roleColors[role] || chalk.white(role.toUpperCase());
    }
}
//# sourceMappingURL=message-history.js.map