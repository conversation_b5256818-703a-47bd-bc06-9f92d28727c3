{"version": 3, "file": "retry-logic.d.ts", "sourceRoot": "", "sources": ["../../src/core/retry-logic.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAItC,MAAM,WAAW,YAAY;IAC3B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC;IAC5D,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IAClD,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,MAAM,CAAC;IAChB,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,KAAK,GAAG,IAAI,CAAC;IACxB,SAAS,EAAE,IAAI,CAAC;IAChB,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,WAAW,EAAE,OAAO,CAAC;CACtB;AAED,qBAAa,UAAW,SAAQ,YAAY;IAC1C,OAAO,CAAC,cAAc,CAA6D;;IAenF;;OAEG;IACU,gBAAgB,CAAC,CAAC,EAC7B,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACpB,OAAO,GAAE,YAAiB,GACzB,OAAO,CAAC,CAAC,CAAC;IAoFb;;OAEG;YACW,kBAAkB;IAqBhC;;OAEG;IACH,OAAO,CAAC,cAAc;IActB;;OAEG;IACH,OAAO,CAAC,WAAW;IAUnB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAoCxB;;OAEG;IACH,OAAO,CAAC,KAAK;IAIb;;OAEG;WACW,0BAA0B,IAAI,YAAY;IAsBxD;;OAEG;WACW,4BAA4B,IAAI,YAAY;IAqB1D;;OAEG;WACW,8BAA8B,IAAI,YAAY;IAkC5D;;OAEG;WACW,+BAA+B,IAAI,YAAY;IAqB7D;;OAEG;WACiB,sBAAsB,CAAC,CAAC,EAC1C,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACpB,WAAW,GAAE,MAAU,EACvB,SAAS,GAAE,MAAa,GACvB,OAAO,CAAC,CAAC,CAAC;IAUb;;OAEG;WACiB,iBAAiB,CAAC,CAAC,EACrC,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACpB,WAAW,GAAE,MAAU,EACvB,KAAK,GAAE,MAAa,GACnB,OAAO,CAAC,CAAC,CAAC;IAUb;;OAEG;WACiB,mBAAmB,CAAC,CAAC,EACvC,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACpB,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,OAAO,EAC1D,WAAW,GAAE,MAAU,GACtB,OAAO,CAAC,CAAC,CAAC;IAQb;;OAEG;WACW,kBAAkB,CAAC,OAAO,EAAE,YAAY,GAAG,MAAM;CAYhE"}