import { EventEmitter } from 'events';
import { LLMMessage } from '@/types';
export interface ChatInputOptions {
    placeholder?: string;
    maxLength?: number;
    multiline?: boolean;
    autoComplete?: boolean;
    history?: boolean;
    historySize?: number;
}
export interface InputValidationResult {
    isValid: boolean;
    error?: string;
    warnings?: string[];
}
export declare class ChatInputProcessor extends EventEmitter {
    private inputHistory;
    private historyIndex;
    private currentInput;
    private options;
    private autoCompleteCache;
    constructor(options?: ChatInputOptions);
    /**
     * Process user input and create a formatted message
     */
    processInput(input: string): LLMMessage | null;
    /**
     * Validate user input
     */
    private validateInput;
    /**
     * Add input to history
     */
    private addToHistory;
    /**
     * Get previous input from history
     */
    getPreviousInput(): string | null;
    /**
     * Get next input from history
     */
    getNextInput(): string | null;
    /**
     * Get auto-complete suggestions
     */
    getAutoCompleteSuggestions(input: string): string[];
    /**
     * Add auto-complete suggestions to cache
     */
    addAutoCompleteSuggestions(command: string, suggestions: string[]): void;
    /**
     * Format input for display
     */
    formatInputForDisplay(input: string): string;
    /**
     * Get input statistics
     */
    getInputStats(): {
        historySize: number;
        averageLength: number;
        mostUsedCommands: Array<{
            command: string;
            count: number;
        }>;
    };
    /**
     * Clear input history
     */
    clearHistory(): void;
    /**
     * Export input history
     */
    exportHistory(): string[];
    /**
     * Import input history
     */
    importHistory(history: string[]): void;
    /**
     * Set current input (for external updates)
     */
    setCurrentInput(input: string): void;
    /**
     * Get current input
     */
    getCurrentInput(): string;
}
//# sourceMappingURL=chat-input.d.ts.map