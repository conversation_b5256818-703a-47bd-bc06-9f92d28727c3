import { EventEmitter } from 'events';
import { ConfigManager } from './config';
import { SessionManager } from './session-manager';
import { LLMManager } from './llm-manager';
import { ToolManager } from './tool-manager';
import { ModernSpinner } from '@/components/spinner';
import { SlashCommandManager } from '@/components/slash-commands';
import { nanoid } from 'nanoid';
export class TerminalEngine extends EventEmitter {
    state;
    configManager;
    sessionManager;
    llmManager;
    toolManager;
    slashCommandManager;
    isProcessing = false;
    constructor() {
        super();
        this.configManager = ConfigManager.getInstance();
        this.sessionManager = new SessionManager();
        this.llmManager = new LLMManager();
        this.toolManager = new ToolManager();
        this.slashCommandManager = new SlashCommandManager(this.configManager, this.sessionManager, this.llmManager);
        this.state = {
            currentSession: null,
            sessions: [],
            config: this.configManager.getAll(),
            isProcessing: false,
            currentCommand: '',
            messageHistory: []
        };
        this.initializeState();
    }
    initializeState() {
        // Load existing sessions
        this.state.sessions = this.sessionManager.getAllSessions();
        // Set current session if configured
        const sessionId = this.configManager.get('sessionId');
        if (sessionId) {
            try {
                this.state.currentSession = this.sessionManager.setCurrentSession(sessionId);
            }
            catch {
                // Session not found, create new one
                this.state.currentSession = this.sessionManager.createSession();
            }
        }
        else {
            // Create initial session
            this.state.currentSession = this.sessionManager.createSession();
        }
        this.state.messageHistory = this.sessionManager.getMessages();
    }
    async processUserInput(input) {
        if (this.isProcessing) {
            this.emit('error', new Error('Already processing a request. Please wait.'));
            return;
        }
        try {
            this.isProcessing = true;
            this.state.isProcessing = true;
            this.state.currentCommand = input;
            this.emitStateChange();
            // Check if it's a slash command
            if (input.startsWith('/')) {
                const handled = await this.slashCommandManager.executeCommand(input);
                if (handled) {
                    return;
                }
            }
            // Process as regular chat message
            await this.processChatMessage(input);
        }
        catch (error) {
            this.emit('error', error);
        }
        finally {
            this.isProcessing = false;
            this.state.isProcessing = false;
            this.state.currentCommand = '';
            this.emitStateChange();
        }
    }
    async processChatMessage(input) {
        // Create user message
        const userMessage = {
            id: nanoid(),
            role: 'user',
            content: input,
            timestamp: new Date()
        };
        // Add to session
        this.sessionManager.addMessage(userMessage);
        this.updateMessageHistory();
        this.emitMessage(userMessage);
        // Get conversation context
        const messages = this.getConversationContext();
        // Get tool definitions
        const tools = this.toolManager.getToolDefinitions();
        // Generate AI response
        const spinner = ModernSpinner.createThinkingSpinner('AI is thinking...');
        try {
            spinner.start();
            const aiResponse = await this.llmManager.generateResponse(messages, tools);
            spinner.succeed('Response generated');
            // Add AI response to session
            this.sessionManager.addMessage(aiResponse);
            this.updateMessageHistory();
            this.emitMessage(aiResponse);
            // Process tool calls if any
            if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
                await this.processToolCalls(aiResponse.toolCalls);
            }
        }
        catch (error) {
            spinner.fail('Failed to generate response');
            const errorMessage = {
                id: nanoid(),
                role: 'system',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
                timestamp: new Date()
            };
            this.sessionManager.addMessage(errorMessage);
            this.updateMessageHistory();
            this.emitMessage(errorMessage);
        }
    }
    async processToolCalls(toolCalls) {
        const spinner = ModernSpinner.createExecutingSpinner('Executing tools...');
        try {
            spinner.start();
            // Execute tool calls
            const toolResults = [];
            for (const toolCall of toolCalls) {
                this.emit('tool-execution-start', { toolCall });
                const result = await this.toolManager.executeToolCall(toolCall);
                toolResults.push(result);
                this.emit('tool-execution-complete', { toolCall, result });
                // Create tool result message
                const toolMessage = {
                    id: nanoid(),
                    role: 'tool',
                    content: result.result,
                    toolCallId: result.toolCallId,
                    timestamp: new Date()
                };
                this.sessionManager.addMessage(toolMessage);
                this.updateMessageHistory();
                this.emitMessage(toolMessage);
            }
            spinner.succeed(`Executed ${toolResults.length} tool(s)`);
            // Generate follow-up response if needed
            const hasFailures = toolResults.some(r => !r.success);
            if (hasFailures || toolResults.some(r => r.result.trim())) {
                await this.generateFollowUpResponse();
            }
        }
        catch (error) {
            spinner.fail('Tool execution failed');
            throw error;
        }
    }
    async generateFollowUpResponse() {
        const spinner = ModernSpinner.createThinkingSpinner('Generating follow-up response...');
        try {
            spinner.start();
            const messages = this.getConversationContext();
            const tools = this.toolManager.getToolDefinitions();
            const followUpResponse = await this.llmManager.generateResponse(messages, tools);
            spinner.succeed('Follow-up response generated');
            this.sessionManager.addMessage(followUpResponse);
            this.updateMessageHistory();
            this.emitMessage(followUpResponse);
            // Handle recursive tool calls if any
            if (followUpResponse.toolCalls && followUpResponse.toolCalls.length > 0) {
                await this.processToolCalls(followUpResponse.toolCalls);
            }
        }
        catch (error) {
            spinner.fail('Failed to generate follow-up response');
            console.error('Follow-up response error:', error);
        }
    }
    getConversationContext() {
        const messages = this.sessionManager.getMessages();
        // Add system prompt
        const systemPrompt = {
            id: nanoid(),
            role: 'system',
            content: this.getSystemPrompt(),
            timestamp: new Date()
        };
        return [systemPrompt, ...messages];
    }
    getSystemPrompt() {
        const config = this.configManager.getAll();
        return `You are Arien AI, a powerful CLI assistant that can help users with various tasks through natural language interaction and function calling.

CORE CAPABILITIES:
- Execute shell commands and system operations
- File and directory management
- Process management and system monitoring
- Development tasks (git, npm, build tools, etc.)
- Network operations and API testing
- Data processing and analysis

IMPORTANT GUIDELINES:
1. Always be helpful, accurate, and safe
2. Ask for confirmation before executing potentially destructive commands
3. Provide clear explanations of what you're doing
4. Use appropriate tools for each task
5. Handle errors gracefully and provide helpful feedback
6. Respect user's working directory and environment

FUNCTION CALLING:
- Use the execute_shell_command tool for system operations
- Always validate commands before execution
- Provide detailed output and error handling
- Use sequential execution for dependent operations
- Use parallel execution for independent operations

CURRENT CONTEXT:
- Working Directory: ${config.workingDirectory}
- Provider: ${config.provider}
- Model: ${config.model}
- Auto-approve: ${config.autoApprove ? 'enabled' : 'disabled'}

Remember to be conversational, helpful, and always prioritize user safety and system integrity.`;
    }
    updateMessageHistory() {
        this.state.messageHistory = this.sessionManager.getMessages();
        this.emitStateChange();
    }
    emitMessage(message) {
        const event = {
            type: 'message',
            data: message,
            timestamp: new Date()
        };
        this.emit('message', event);
    }
    emitStateChange() {
        this.emit('state-change', this.state);
    }
    // Public API methods
    getState() {
        return { ...this.state };
    }
    getCurrentSession() {
        return this.state.currentSession;
    }
    createNewSession(name) {
        const session = this.sessionManager.createSession(name);
        this.state.currentSession = session;
        this.state.sessions = this.sessionManager.getAllSessions();
        this.updateMessageHistory();
        this.emitStateChange();
        return session;
    }
    switchSession(sessionId) {
        const session = this.sessionManager.setCurrentSession(sessionId);
        this.state.currentSession = session;
        this.updateMessageHistory();
        this.emitStateChange();
        return session;
    }
    deleteSession(sessionId) {
        const success = this.sessionManager.deleteSession(sessionId);
        if (success) {
            this.state.sessions = this.sessionManager.getAllSessions();
            // If current session was deleted, create a new one
            if (this.state.currentSession?.id === sessionId) {
                this.state.currentSession = this.sessionManager.createSession();
                this.updateMessageHistory();
            }
            this.emitStateChange();
        }
        return success;
    }
    clearCurrentSession() {
        this.sessionManager.clearMessages();
        this.updateMessageHistory();
        this.emitStateChange();
    }
    async switchProvider(provider, model) {
        await this.llmManager.switchProvider(provider, model);
        this.state.config = this.configManager.getAll();
        this.emitStateChange();
    }
    async switchModel(model) {
        await this.llmManager.switchModel(model);
        this.state.config = this.configManager.getAll();
        this.emitStateChange();
    }
    setWorkingDirectory(directory) {
        this.configManager.set('workingDirectory', directory);
        this.toolManager.setWorkingDirectory(directory);
        this.state.config = this.configManager.getAll();
        this.emitStateChange();
    }
    getAvailableCommands() {
        return this.slashCommandManager.getCommands().map(cmd => `/${cmd.name}`);
    }
    isReady() {
        return this.configManager.isConfigured() && !this.isProcessing;
    }
    async initialize() {
        if (this.configManager.isConfigured()) {
            try {
                await this.llmManager.initializeProvider();
            }
            catch (error) {
                console.warn('Failed to initialize LLM provider:', error);
            }
        }
    }
    destroy() {
        this.removeAllListeners();
        ModernSpinner.stopAll();
    }
}
//# sourceMappingURL=terminal-engine.js.map