import { Config } from '@/types';
export interface OnboardingResult {
    config: Config;
    success: boolean;
    message: string;
    autoStart?: boolean;
}
export declare class OnboardingFlow {
    private configManager;
    private llmManager;
    constructor();
    start(): Promise<OnboardingResult>;
    private showWelcome;
    private askReconfigure;
    private askAutoStart;
    private selectProvider;
    private configureProvider;
    private configureDeepseek;
    private configureOllama;
    private testConnection;
    quickSetup(): Promise<OnboardingResult>;
}
//# sourceMappingURL=onboarding.d.ts.map