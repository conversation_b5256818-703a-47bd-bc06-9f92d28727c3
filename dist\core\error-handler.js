import { EventEmitter } from 'events';
import { ProviderError, CommandError, ConfigError } from '@/types';
import { RetryLogic } from './retry-logic';
import chalk from 'chalk';
import { format } from 'date-fns';
export class ErrorHandler extends EventEmitter {
    options;
    errorHistory = [];
    retryLogic;
    constructor(options = {}) {
        super();
        this.options = {
            enableLogging: true,
            enableRetry: true,
            enableUserNotification: true,
            logLevel: 'error',
            maxErrorHistory: 100,
            ...options
        };
        this.retryLogic = new RetryLogic();
        this.setupGlobalErrorHandlers();
    }
    /**
     * Setup global error handlers
     */
    setupGlobalErrorHandlers() {
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            this.handleError(error, {
                timestamp: new Date(),
                operation: 'global',
                component: 'process'
            }, 'critical');
        });
        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            const error = reason instanceof Error ? reason : new Error(String(reason));
            this.handleError(error, {
                timestamp: new Date(),
                operation: 'promise',
                component: 'process'
            }, 'high');
        });
    }
    /**
     * Main error handling method
     */
    async handleError(error, context, severity = 'medium') {
        const report = this.createErrorReport(error, context, severity);
        // Add to history
        this.addToHistory(report);
        // Log error
        if (this.options.enableLogging) {
            this.logError(report);
        }
        // Emit error event
        this.emit('error-handled', report);
        // Notify user if enabled
        if (this.options.enableUserNotification) {
            this.notifyUser(report);
        }
        return report;
    }
    /**
     * Create detailed error report
     */
    createErrorReport(error, context, severity) {
        const report = {
            id: this.generateErrorId(),
            error,
            context,
            severity,
            category: this.categorizeError(error),
            isRetryable: this.isRetryableError(error),
            suggestedActions: this.generateSuggestedActions(error),
            stackTrace: error.stack
        };
        return report;
    }
    /**
     * Generate unique error ID
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Categorize error type
     */
    categorizeError(error) {
        if (error instanceof ProviderError) {
            return 'provider';
        }
        if (error instanceof CommandError) {
            return 'command';
        }
        if (error instanceof ConfigError) {
            return 'config';
        }
        // Check error message patterns
        const message = error.message.toLowerCase();
        if (/network|connection|timeout|dns|socket/i.test(message)) {
            return 'network';
        }
        if (/permission|access|unauthorized|forbidden/i.test(message)) {
            return 'system';
        }
        if (/invalid|syntax|parse|format/i.test(message)) {
            return 'user';
        }
        return 'unknown';
    }
    /**
     * Check if error is retryable
     */
    isRetryableError(error) {
        const retryablePatterns = [
            /network/i,
            /timeout/i,
            /rate limit/i,
            /temporary/i,
            /overloaded/i,
            /503|502|504/i
        ];
        return retryablePatterns.some(pattern => pattern.test(error.message));
    }
    /**
     * Generate suggested actions for error
     */
    generateSuggestedActions(error) {
        const actions = [];
        const message = error.message.toLowerCase();
        // Network-related suggestions
        if (/network|connection|timeout/i.test(message)) {
            actions.push('Check your internet connection');
            actions.push('Verify firewall settings');
            actions.push('Try again in a few moments');
        }
        // Provider-related suggestions
        if (error instanceof ProviderError || /provider|api|key/i.test(message)) {
            actions.push('Verify your API key is correct');
            actions.push('Check provider service status');
            actions.push('Try switching to a different model');
        }
        // Configuration suggestions
        if (error instanceof ConfigError || /config|setting/i.test(message)) {
            actions.push('Run configuration setup: arien config');
            actions.push('Check configuration file permissions');
            actions.push('Reset configuration if needed');
        }
        // Command execution suggestions
        if (error instanceof CommandError || /command|execution/i.test(message)) {
            actions.push('Check command syntax');
            actions.push('Verify file permissions');
            actions.push('Ensure required tools are installed');
        }
        // Rate limiting suggestions
        if (/rate limit|quota|throttle/i.test(message)) {
            actions.push('Wait before retrying');
            actions.push('Check your API usage limits');
            actions.push('Consider upgrading your plan');
        }
        // Generic fallback
        if (actions.length === 0) {
            actions.push('Check the error details above');
            actions.push('Try the operation again');
            actions.push('Contact support if the issue persists');
        }
        return actions;
    }
    /**
     * Log error with appropriate level
     */
    logError(report) {
        const timestamp = format(report.context.timestamp, 'yyyy-MM-dd HH:mm:ss');
        const errorId = chalk.gray(`[${report.id}]`);
        const severity = this.formatSeverity(report.severity);
        const category = chalk.blue(`[${report.category.toUpperCase()}]`);
        const header = `${timestamp} ${errorId} ${severity} ${category}`;
        const operation = chalk.cyan(`Operation: ${report.context.operation}`);
        const component = chalk.yellow(`Component: ${report.context.component}`);
        const message = chalk.red(`Error: ${report.error.message}`);
        console.error(header);
        console.error(`  ${operation}`);
        console.error(`  ${component}`);
        console.error(`  ${message}`);
        // Show suggested actions
        if (report.suggestedActions.length > 0) {
            console.error(chalk.white('  Suggested actions:'));
            report.suggestedActions.forEach(action => {
                console.error(chalk.gray(`    • ${action}`));
            });
        }
        // Show stack trace for high/critical errors
        if (['high', 'critical'].includes(report.severity) && report.stackTrace) {
            console.error(chalk.gray('  Stack trace:'));
            console.error(chalk.gray(report.stackTrace.split('\n').map(line => `    ${line}`).join('\n')));
        }
        console.error(''); // Empty line for readability
    }
    /**
     * Format severity for display
     */
    formatSeverity(severity) {
        const severityColors = {
            low: chalk.green('LOW'),
            medium: chalk.yellow('MEDIUM'),
            high: chalk.red('HIGH'),
            critical: chalk.bgRed.white('CRITICAL')
        };
        return severityColors[severity];
    }
    /**
     * Notify user about error
     */
    notifyUser(report) {
        this.emit('user-notification', {
            type: 'error',
            severity: report.severity,
            message: this.formatUserMessage(report),
            actions: report.suggestedActions
        });
    }
    /**
     * Format user-friendly error message
     */
    formatUserMessage(report) {
        const categoryMessages = {
            network: 'Network connection issue',
            provider: 'AI provider error',
            command: 'Command execution failed',
            config: 'Configuration error',
            system: 'System error',
            user: 'Input error',
            unknown: 'Unexpected error'
        };
        const categoryMessage = categoryMessages[report.category];
        return `${categoryMessage}: ${report.error.message}`;
    }
    /**
     * Add error to history
     */
    addToHistory(report) {
        this.errorHistory.unshift(report);
        // Limit history size
        if (this.errorHistory.length > this.options.maxErrorHistory) {
            this.errorHistory = this.errorHistory.slice(0, this.options.maxErrorHistory);
        }
    }
    /**
     * Execute operation with error handling and retry
     */
    async executeWithErrorHandling(operation, context, options = {}) {
        const fullContext = {
            ...context,
            timestamp: new Date()
        };
        try {
            if (this.options.enableRetry && options.retryOptions) {
                return await this.retryLogic.executeWithRetry(operation, options.retryOptions);
            }
            else {
                return await operation();
            }
        }
        catch (error) {
            const report = await this.handleError(error, fullContext, options.severity);
            throw error;
        }
    }
    /**
     * Get error statistics
     */
    getErrorStatistics() {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const errorsByCategory = {};
        const errorsBySeverity = {};
        const errorMessages = {};
        let recentErrors = 0;
        for (const report of this.errorHistory) {
            // Count by category
            errorsByCategory[report.category] = (errorsByCategory[report.category] || 0) + 1;
            // Count by severity
            errorsBySeverity[report.severity] = (errorsBySeverity[report.severity] || 0) + 1;
            // Count recent errors
            if (report.context.timestamp > oneHourAgo) {
                recentErrors++;
            }
            // Count error messages
            const message = report.error.message;
            errorMessages[message] = (errorMessages[message] || 0) + 1;
        }
        // Find most common error
        const mostCommonError = Object.entries(errorMessages)
            .sort(([, a], [, b]) => b - a)[0]?.[0] || null;
        return {
            totalErrors: this.errorHistory.length,
            errorsByCategory,
            errorsBySeverity,
            recentErrors,
            mostCommonError
        };
    }
    /**
     * Get recent errors
     */
    getRecentErrors(count = 10) {
        return this.errorHistory.slice(0, count);
    }
    /**
     * Clear error history
     */
    clearErrorHistory() {
        this.errorHistory = [];
        this.emit('history-cleared');
    }
    /**
     * Export error history
     */
    exportErrorHistory() {
        return JSON.stringify({
            exportDate: new Date().toISOString(),
            totalErrors: this.errorHistory.length,
            errors: this.errorHistory.map(report => ({
                id: report.id,
                timestamp: report.context.timestamp.toISOString(),
                operation: report.context.operation,
                component: report.context.component,
                severity: report.severity,
                category: report.category,
                message: report.error.message,
                isRetryable: report.isRetryable,
                suggestedActions: report.suggestedActions
            }))
        }, null, 2);
    }
    /**
     * Create error handler for specific component
     */
    createComponentHandler(component) {
        return {
            handleError: (error, operation, severity) => {
                return this.handleError(error, {
                    timestamp: new Date(),
                    operation,
                    component
                }, severity);
            },
            executeWithHandling: (operation, operationName, options) => {
                return this.executeWithErrorHandling(operation, {
                    operation: operationName,
                    component
                }, options);
            }
        };
    }
}
//# sourceMappingURL=error-handler.js.map