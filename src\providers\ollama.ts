import axios, { AxiosInstance, AxiosError } from 'axios';
import { LLMMessage, ToolCall, ToolDefinition, ProviderError } from '@/types';
import pRetry from 'p-retry';
import pTimeout from 'p-timeout';

export interface OllamaConfig {
  baseUrl: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
}

export interface OllamaResponse {
  model: string;
  created_at: string;
  message: {
    role: string;
    content: string;
    tool_calls?: Array<{
      function: {
        name: string;
        arguments: Record<string, any>;
      };
    }>;
  };
  done: boolean;
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export class OllamaProvider {
  private client: AxiosInstance;
  private config: OllamaConfig;

  constructor(config: OllamaConfig) {
    this.config = {
      maxTokens: 4096,
      temperature: 0.7,
      timeout: 60000, // Ollama can be slower than cloud APIs
      ...config,
      baseUrl: config.baseUrl || 'http://localhost:11434'
    };

    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Arien-AI-CLI/1.0.0'
      }
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        if (error.code === 'ECONNREFUSED') {
          throw new ProviderError(
            'Cannot connect to Ollama server. Please ensure Ollama is running.',
            { baseUrl: this.config.baseUrl }
          );
        }

        if (error.code === 'ECONNABORTED') {
          throw new ProviderError('Request timeout. The model might be loading or the request is too complex.', {
            timeout: this.config.timeout
          });
        }

        if (error.response?.status === 404) {
          throw new ProviderError(
            `Model '${this.config.model}' not found. Please pull the model first using 'ollama pull ${this.config.model}'`,
            { model: this.config.model }
          );
        }

        throw new ProviderError(
          `Ollama API request failed: ${error.message}`,
          { 
            status: error.response?.status,
            data: error.response?.data 
          }
        );
      }
    );
  }

  public async generateResponse(
    messages: LLMMessage[],
    tools?: ToolDefinition[],
    options: {
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
    } = {}
  ): Promise<LLMMessage> {
    const requestConfig = {
      model: this.config.model,
      messages: this.formatMessages(messages),
      stream: false, // We'll handle streaming separately if needed
      options: {
        num_predict: options.maxTokens || this.config.maxTokens,
        temperature: options.temperature || this.config.temperature,
        top_k: 40,
        top_p: 0.9,
        repeat_penalty: 1.1
      },
      tools: tools ? this.formatTools(tools) : undefined
    };

    try {
      const response = await pRetry(
        async () => {
          const result = await pTimeout(
            this.client.post<OllamaResponse>('/api/chat', requestConfig),
            { milliseconds: this.config.timeout! }
          );
          return result;
        },
        {
          retries: 2, // Fewer retries for local server
          factor: 1.5,
          minTimeout: 2000,
          maxTimeout: 5000,
          onFailedAttempt: (error) => {
            console.warn(`Attempt ${error.attemptNumber} failed. ${error.retriesLeft} retries left.`);
          }
        }
      );

      const message = response.data.message;
      const toolCalls = message.tool_calls?.map((tc, index) => ({
        id: `call_${Date.now()}_${index}`,
        type: 'function' as const,
        function: {
          name: tc.function.name,
          arguments: JSON.stringify(tc.function.arguments)
        }
      })) as ToolCall[] | undefined;

      return {
        id: `ollama_${Date.now()}`,
        role: 'assistant',
        content: message.content || '',
        toolCalls,
        timestamp: new Date()
      };

    } catch (error) {
      if (error instanceof ProviderError) {
        throw error;
      }
      
      throw new ProviderError(
        `Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { originalError: error }
      );
    }
  }

  public async validateConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/api/tags');
      return response.status === 200;
    } catch (error) {
      console.warn('Ollama connection validation failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get<{ models: OllamaModel[] }>('/api/tags');
      return response.data.models.map(model => model.name);
    } catch (error) {
      console.warn('Failed to fetch available models:', error);
      return []; // Return empty array if we can't fetch models
    }
  }

  public async pullModel(modelName: string): Promise<void> {
    try {
      await this.client.post('/api/pull', { name: modelName });
    } catch (error) {
      throw new ProviderError(
        `Failed to pull model '${modelName}': ${error instanceof Error ? error.message : 'Unknown error'}`,
        { model: modelName }
      );
    }
  }

  public async deleteModel(modelName: string): Promise<void> {
    try {
      await this.client.delete('/api/delete', { data: { name: modelName } });
    } catch (error) {
      throw new ProviderError(
        `Failed to delete model '${modelName}': ${error instanceof Error ? error.message : 'Unknown error'}`,
        { model: modelName }
      );
    }
  }

  public async getModelInfo(modelName: string): Promise<any> {
    try {
      const response = await this.client.post('/api/show', { name: modelName });
      return response.data;
    } catch (error) {
      throw new ProviderError(
        `Failed to get model info for '${modelName}': ${error instanceof Error ? error.message : 'Unknown error'}`,
        { model: modelName }
      );
    }
  }

  private formatMessages(messages: LLMMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      tool_calls: msg.toolCalls?.map(tc => ({
        function: {
          name: tc.function.name,
          arguments: JSON.parse(tc.function.arguments)
        }
      }))
    }));
  }

  private formatTools(tools: ToolDefinition[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  public updateConfig(newConfig: Partial<OllamaConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update base URL if changed
    if (newConfig.baseUrl) {
      this.client.defaults.baseURL = newConfig.baseUrl;
    }
    
    // Update timeout if changed
    if (newConfig.timeout) {
      this.client.defaults.timeout = newConfig.timeout;
    }
  }

  public getConfig(): OllamaConfig {
    return { ...this.config };
  }
}
