{"version": 3, "file": "ollama.js", "sourceRoot": "", "sources": ["../../src/providers/ollama.ts"], "names": [], "mappings": "AAAA,OAAO,KAAoC,MAAM,OAAO,CAAC;AACzD,OAAO,EAAwC,aAAa,EAAE,MAAM,SAAS,CAAC;AAC9E,OAAO,MAAM,MAAM,SAAS,CAAC;AAC7B,OAAO,QAAQ,MAAM,WAAW,CAAC;AAgDjC,MAAM,OAAO,cAAc;IACjB,MAAM,CAAgB;IACtB,MAAM,CAAe;IAE7B,YAAY,MAAoB;QAC9B,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;YAChB,OAAO,EAAE,KAAK,EAAE,uCAAuC;YACvD,GAAG,MAAM;YACT,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,wBAAwB;SACpD,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,YAAY,EAAE,oBAAoB;aACnC;SACF,CAAC,CAAC;QAEH,8CAA8C;QAC9C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EACtB,CAAC,KAAiB,EAAE,EAAE;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,MAAM,IAAI,aAAa,CACrB,mEAAmE,EACnE,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CACjC,CAAC;YACJ,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,MAAM,IAAI,aAAa,CAAC,4EAA4E,EAAE;oBACpG,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B,CAAC,CAAC;YACL,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACnC,MAAM,IAAI,aAAa,CACrB,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,+DAA+D,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAC9G,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,aAAa,CACrB,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAC7C;gBACE,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;aAC3B,CACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,QAAsB,EACtB,KAAwB,EACxB,UAII,EAAE;QAEN,MAAM,aAAa,GAAG;YACpB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvC,MAAM,EAAE,KAAK,EAAE,8CAA8C;YAC7D,OAAO,EAAE;gBACP,WAAW,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;gBAC3D,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,GAAG;gBACV,cAAc,EAAE,GAAG;aACpB;YACD,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SACnD,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAC3B,KAAK,IAAI,EAAE;gBACT,MAAM,MAAM,GAAG,MAAM,QAAQ,CAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAiB,WAAW,EAAE,aAAa,CAAC,EAC5D,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,OAAQ,EAAE,CACvC,CAAC;gBACF,OAAO,MAAM,CAAC;YAChB,CAAC,EACD;gBACE,OAAO,EAAE,CAAC,EAAE,iCAAiC;gBAC7C,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;gBAChB,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE;oBACzB,OAAO,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,aAAa,YAAY,KAAK,CAAC,WAAW,gBAAgB,CAAC,CAAC;gBAC5F,CAAC;aACF,CACF,CAAC;YAEF,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;YACtC,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBACxD,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE;gBACjC,IAAI,EAAE,UAAmB;gBACzB,QAAQ,EAAE;oBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;oBACtB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;iBACjD;aACF,CAAC,CAA2B,CAAC;YAE9B,OAAO;gBACL,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC1B,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;gBAC9B,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,aAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,aAAa,CACrB,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC1F,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACpD,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAA4B,WAAW,CAAC,CAAC;YAC/E,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,EAAE,CAAC,CAAC,8CAA8C;QAC3D,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,SAAiB;QACtC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CACrB,yBAAyB,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAClG,EAAE,KAAK,EAAE,SAAS,EAAE,CACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CACrB,2BAA2B,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACpG,EAAE,KAAK,EAAE,SAAS,EAAE,CACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,SAAiB;QACzC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;YAC1E,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CACrB,iCAAiC,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC1G,EAAE,KAAK,EAAE,SAAS,EAAE,CACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAsB;QAC3C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,UAAU,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACpC,QAAQ,EAAE;oBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;oBACtB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;iBAC7C;aACF,CAAC,CAAC;SACJ,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,WAAW,CAAC,KAAuB;QACzC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,YAAY,CAAC,SAAgC;QAClD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAE/C,6BAA6B;QAC7B,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QACnD,CAAC;QAED,4BAA4B;QAC5B,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QACnD,CAAC;IACH,CAAC;IAEM,SAAS;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF"}