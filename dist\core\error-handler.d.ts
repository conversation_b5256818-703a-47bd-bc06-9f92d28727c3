import { EventEmitter } from 'events';
export interface ErrorContext {
    timestamp: Date;
    operation: string;
    component: string;
    userId?: string;
    sessionId?: string;
    metadata?: Record<string, any>;
}
export interface ErrorReport {
    id: string;
    error: Error;
    context: ErrorContext;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'network' | 'provider' | 'command' | 'config' | 'system' | 'user' | 'unknown';
    isRetryable: boolean;
    suggestedActions: string[];
    stackTrace?: string;
}
export interface ErrorHandlerOptions {
    enableLogging?: boolean;
    enableRetry?: boolean;
    enableUserNotification?: boolean;
    logLevel?: 'error' | 'warn' | 'info' | 'debug';
    maxErrorHistory?: number;
}
export declare class ErrorHandler extends EventEmitter {
    private options;
    private errorHistory;
    private retryLogic;
    constructor(options?: ErrorHandlerOptions);
    /**
     * Setup global error handlers
     */
    private setupGlobalErrorHandlers;
    /**
     * Main error handling method
     */
    handleError(error: Error, context: ErrorContext, severity?: ErrorReport['severity']): Promise<ErrorReport>;
    /**
     * Create detailed error report
     */
    private createErrorReport;
    /**
     * Generate unique error ID
     */
    private generateErrorId;
    /**
     * Categorize error type
     */
    private categorizeError;
    /**
     * Check if error is retryable
     */
    private isRetryableError;
    /**
     * Generate suggested actions for error
     */
    private generateSuggestedActions;
    /**
     * Log error with appropriate level
     */
    private logError;
    /**
     * Format severity for display
     */
    private formatSeverity;
    /**
     * Notify user about error
     */
    private notifyUser;
    /**
     * Format user-friendly error message
     */
    private formatUserMessage;
    /**
     * Add error to history
     */
    private addToHistory;
    /**
     * Execute operation with error handling and retry
     */
    executeWithErrorHandling<T>(operation: () => Promise<T>, context: Omit<ErrorContext, 'timestamp'>, options?: {
        retryOptions?: any;
        severity?: ErrorReport['severity'];
    }): Promise<T>;
    /**
     * Get error statistics
     */
    getErrorStatistics(): {
        totalErrors: number;
        errorsByCategory: Record<string, number>;
        errorsBySeverity: Record<string, number>;
        recentErrors: number;
        mostCommonError: string | null;
    };
    /**
     * Get recent errors
     */
    getRecentErrors(count?: number): ErrorReport[];
    /**
     * Clear error history
     */
    clearErrorHistory(): void;
    /**
     * Export error history
     */
    exportErrorHistory(): string;
    /**
     * Create error handler for specific component
     */
    createComponentHandler(component: string): {
        handleError: (error: Error, operation: string, severity?: ErrorReport["severity"]) => Promise<ErrorReport>;
        executeWithHandling: <T>(operation: () => Promise<T>, operationName: string, options?: any) => Promise<T>;
    };
}
//# sourceMappingURL=error-handler.d.ts.map