{"version": 3, "file": "terminal-engine.js", "sourceRoot": "", "sources": ["../../src/core/terminal-engine.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEtC,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAC;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAGhC,MAAM,OAAO,cAAe,SAAQ,YAAY;IACtC,KAAK,CAAgB;IACrB,aAAa,CAAgB;IAC7B,cAAc,CAAiB;IAC/B,UAAU,CAAa;IACvB,WAAW,CAAc;IACzB,mBAAmB,CAAsB;IACzC,YAAY,GAAY,KAAK,CAAC;IAEtC;QACE,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;QAErC,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,CAChD,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,UAAU,CAChB,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG;YACX,cAAc,EAAE,IAAI;YACpB,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACnC,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;SACnB,CAAC;QAEF,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,eAAe;QACrB,yBAAyB;QACzB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAE3D,oCAAoC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtD,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC;gBACH,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC/E,CAAC;YAAC,MAAM,CAAC;gBACP,oCAAoC;gBACpC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YAClE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,yBAAyB;YACzB,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;IAChE,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,KAAa;QACzC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;YAC5E,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;YAClC,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,gCAAgC;YAChC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACrE,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO;gBACT,CAAC;YACH,CAAC;YAED,kCAAkC;YAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAEvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAa;QAC5C,sBAAsB;QACtB,MAAM,WAAW,GAAe;YAC9B,EAAE,EAAE,MAAM,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,iBAAiB;QACjB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAE9B,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE/C,uBAAuB;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;QAEpD,uBAAuB;QACvB,MAAM,OAAO,GAAG,aAAa,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;QAEzE,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAEhB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAE3E,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAEtC,6BAA6B;YAC7B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC3C,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAE7B,4BAA4B;YAC5B,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACpD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAE5C,MAAM,YAAY,GAAe;gBAC/B,EAAE,EAAE,MAAM,EAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EAAE;gBACtF,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC7C,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,SAAqB;QAClD,MAAM,OAAO,GAAG,aAAa,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAEhB,qBAAqB;YACrB,MAAM,WAAW,GAAiB,EAAE,CAAC;YAErC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAEhD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAChE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEzB,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;gBAE3D,6BAA6B;gBAC7B,MAAM,WAAW,GAAe;oBAC9B,EAAE,EAAE,MAAM,EAAE;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;oBACtB,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC5C,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,YAAY,WAAW,CAAC,MAAM,UAAU,CAAC,CAAC;YAE1D,wCAAwC;YACxC,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACxC,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,MAAM,OAAO,GAAG,aAAa,CAAC,qBAAqB,CAAC,kCAAkC,CAAC,CAAC;QAExF,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEjF,OAAO,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAEhD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YACjD,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YAEnC,qCAAqC;YACrC,IAAI,gBAAgB,CAAC,SAAS,IAAI,gBAAgB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxE,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC1D,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACtD,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;QAEnD,oBAAoB;QACpB,MAAM,YAAY,GAAe;YAC/B,EAAE,EAAE,MAAM,EAAE;YACZ,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,OAAO,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,CAAC;IACrC,CAAC;IAEO,eAAe;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAE3C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;uBA0BY,MAAM,CAAC,gBAAgB;cAChC,MAAM,CAAC,QAAQ;WAClB,MAAM,CAAC,KAAK;kBACL,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;;gGAEmC,CAAC;IAC/F,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;QAC9D,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,WAAW,CAAC,OAAmB;QACrC,MAAM,KAAK,GAAkB;YAC3B,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,qBAAqB;IACd,QAAQ;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAEM,gBAAgB,CAAC,IAAa;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAC3D,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,aAAa,CAAC,SAAiB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,aAAa,CAAC,SAAiB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YAE3D,mDAAmD;YACnD,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,KAAK,SAAS,EAAE,CAAC;gBAChD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;gBAChE,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,mBAAmB;QACxB,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAA+B,EAAE,KAAc;QACzE,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAChD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,KAAa;QACpC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAChD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEM,mBAAmB,CAAC,SAAiB;QAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAChD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEM,oBAAoB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE,CAAC;YACtC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAC7C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,aAAa,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;CACF"}