import { ToolDefinition, ToolCall, ToolResult, LLMMessage } from '@/types';
export declare class ToolManager {
    private tools;
    private shellTool;
    constructor(workingDirectory?: string);
    private registerDefaultTools;
    registerTool(name: string, tool: any): void;
    getToolDefinitions(): ToolDefinition[];
    getToolDefinition(name: string): ToolDefinition | null;
    executeToolCall(toolCall: ToolCall): Promise<ToolResult>;
    private executeShellCommand;
    private formatToolResult;
    executeToolCalls(toolCalls: ToolCall[]): Promise<ToolResult[]>;
    createToolResultMessages(toolResults: ToolResult[]): LLMMessage[];
    setWorkingDirectory(directory: string): void;
    getWorkingDirectory(): string;
    validateWorkingDirectory(directory: string): Promise<boolean>;
    getCurrentDirectory(): Promise<string>;
    getAvailableTools(): string[];
    hasToolCalls(message: LLMMessage): boolean;
    extractToolCalls(message: LLMMessage): ToolCall[];
    processMessageWithTools(message: LLMMessage, onToolExecution?: (toolCall: ToolCall, result: ToolResult) => void): Promise<LLMMessage[]>;
    getToolUsageStats(): {
        totalExecutions: number;
        toolUsage: Record<string, number>;
        averageExecutionTime: number;
    };
}
//# sourceMappingURL=tool-manager.d.ts.map