import ora from 'ora';
import chalk from 'chalk';
export class ModernSpinner {
    spinner;
    static activeSpinners = new Set();
    constructor(options = {}) {
        const defaultOptions = {
            text: 'Processing...',
            color: 'cyan',
            spinner: 'dots12',
            indent: 0,
            interval: 80,
            isEnabled: true,
            isSilent: false,
            discardStdin: true,
            hideCursor: true,
            ...options
        };
        this.spinner = ora(defaultOptions);
        ModernSpinner.activeSpinners.add(this);
    }
    start(text) {
        if (text) {
            this.spinner.text = text;
        }
        this.spinner.start();
        return this;
    }
    stop() {
        this.spinner.stop();
        return this;
    }
    succeed(text) {
        if (text) {
            this.spinner.text = text;
        }
        this.spinner.succeed();
        ModernSpinner.activeSpinners.delete(this);
        return this;
    }
    fail(text) {
        if (text) {
            this.spinner.text = text;
        }
        this.spinner.fail();
        ModernSpinner.activeSpinners.delete(this);
        return this;
    }
    warn(text) {
        if (text) {
            this.spinner.text = text;
        }
        this.spinner.warn();
        ModernSpinner.activeSpinners.delete(this);
        return this;
    }
    info(text) {
        if (text) {
            this.spinner.text = text;
        }
        this.spinner.info();
        ModernSpinner.activeSpinners.delete(this);
        return this;
    }
    setText(text) {
        this.spinner.text = text;
        return this;
    }
    setColor(color) {
        if (color) {
            this.spinner.color = color;
        }
        return this;
    }
    setSpinner(spinner) {
        this.spinner.spinner = spinner;
        return this;
    }
    get isSpinning() {
        return this.spinner.isSpinning;
    }
    get text() {
        return this.spinner.text;
    }
    static stopAll() {
        ModernSpinner.activeSpinners.forEach(spinner => {
            if (spinner.isSpinning) {
                spinner.stop();
            }
        });
        ModernSpinner.activeSpinners.clear();
    }
    static createProcessingSpinner(text = 'Processing...') {
        return new ModernSpinner({
            text: chalk.cyan(text),
            color: 'cyan',
            spinner: 'dots12'
        });
    }
    static createLoadingSpinner(text = 'Loading...') {
        return new ModernSpinner({
            text: chalk.blue(text),
            color: 'blue',
            spinner: 'bouncingBar'
        });
    }
    static createThinkingSpinner(text = 'AI is thinking...') {
        return new ModernSpinner({
            text: chalk.magenta(text),
            color: 'magenta',
            spinner: 'mindblown'
        });
    }
    static createExecutingSpinner(text = 'Executing command...') {
        return new ModernSpinner({
            text: chalk.yellow(text),
            color: 'yellow',
            spinner: 'runner'
        });
    }
    static createConnectingSpinner(text = 'Connecting...') {
        return new ModernSpinner({
            text: chalk.green(text),
            color: 'green',
            spinner: 'dots'
        });
    }
    static createToolExecutionSpinner(text = 'Executing tool...') {
        return new ModernSpinner({
            text: chalk.yellow(text),
            color: 'yellow',
            spinner: 'dots'
        });
    }
    static createRetrySpinner(text = 'Retrying...') {
        return new ModernSpinner({
            text: chalk.yellow(text),
            color: 'yellow',
            spinner: 'dots'
        });
    }
    // Utility methods for common spinner patterns
    static async withSpinner(promise, options = {}) {
        const spinner = options.spinner || ModernSpinner.createProcessingSpinner(options.text);
        try {
            spinner.start();
            const result = await promise;
            spinner.succeed(options.successText);
            return result;
        }
        catch (error) {
            spinner.fail(options.failText || `Failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    static async withProgressSpinner(promise, progressCallback, options = {}) {
        const spinner = ModernSpinner.createProcessingSpinner(options.initialText);
        try {
            spinner.start();
            // Set up progress callback
            const updateText = (text) => {
                spinner.setText(text);
            };
            progressCallback(updateText);
            const result = await promise;
            spinner.succeed(options.successText);
            return result;
        }
        catch (error) {
            spinner.fail(options.failText || `Failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    // Animation frames for custom spinners
    static CUSTOM_SPINNERS = {
        modernDots: {
            interval: 80,
            frames: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        },
        modernBars: {
            interval: 100,
            frames: ['▁', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃']
        },
        modernCircle: {
            interval: 120,
            frames: ['◐', '◓', '◑', '◒']
        },
        modernArrow: {
            interval: 100,
            frames: ['←', '↖', '↑', '↗', '→', '↘', '↓', '↙']
        },
        modernPulse: {
            interval: 200,
            frames: ['●', '○', '◉', '○']
        }
    };
    static createCustomSpinner(spinnerName, text = 'Processing...', color = 'cyan') {
        const customSpinner = ModernSpinner.CUSTOM_SPINNERS[spinnerName];
        return new ModernSpinner({
            text,
            color,
            spinner: customSpinner
        });
    }
}
//# sourceMappingURL=spinner.js.map