{"version": 3, "file": "chat-output.js", "sourceRoot": "", "sources": ["../../src/components/chat-output.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,SAAS,MAAM,YAAY,CAAC;AAwBnC,MAAM,OAAO,mBAAoB,SAAQ,YAAY;IAC3C,OAAO,CAAgC;IACvC,aAAa,GAAuB,EAAE,CAAC;IACvC,aAAa,GAAW,IAAI,CAAC;IAErC,YAAY,UAA+B,EAAE;QAC3C,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,GAAG;YAClB,gBAAgB,EAAE,IAAI;YACtB,WAAW,EAAE,KAAK;YAClB,cAAc,EAAE,KAAK;YACrB,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAmB,EAAE,QAAc;QACtD,MAAM,SAAS,GAAqB;YAClC,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC5C,UAAU,EAAE,OAAO,CAAC,OAAO;YAC3B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,QAAQ;SACT,CAAC;QAEF,gBAAgB;QAChB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAE5B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAmB;QAC/C,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7C,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEhE,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAE5C,gCAAgC;QAChC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM;YACR,KAAK,WAAW;gBACd,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,MAAM;gBACT,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,QAAQ;gBACX,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAC5C,MAAM;QACV,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC;QAED,oBAAoB;QACpB,MAAM,KAAK,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAY;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YACjC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;QACnC,CAAC;QAED,MAAM,UAAU,GAAG;YACjB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YAChC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;YACpC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;YACnC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;SACxC,CAAC;QAEF,OAAO,UAAU,CAAC,IAA+B,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAe;QACvC,2BAA2B;QAC3B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/D,6CAA6C;QAC7C,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAEhE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAe;QAC5C,qBAAqB;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;QAED,eAAe;QACf,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEpC,kBAAkB;QAClB,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAe,EAAE,UAAmB;QAC5D,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE3E,wBAAwB;QACxB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACtC,wBAAwB;YACxB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClF,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,0BAA0B;YAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClF,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;YACD,0BAA0B;YAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACvF,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC;YACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,MAAM,KAAK,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAe;QACzC,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,SAAqB;QAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC/D,OAAO,MAAM,YAAY,IAAI,IAAI,GAAG,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAY;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;iBACrC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;iBAClF,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe;QACtC,qCAAqC;QACrC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,2BAA2B,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;YAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9C,OAAO,KAAK,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAEpE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,OAAe;QACjC,uBAAuB;QACvB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,uBAAuB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAE5E,wBAAwB;QACxB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;QAEhF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe;QACpC,YAAY;QACZ,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEhE,cAAc;QACd,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAE9D,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,MAAkB;QACxC,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7B,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;YAC1B,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAExB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,aAAa,KAAK,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;QAEhE,IAAI,MAAM,GAAG,GAAG,MAAM,IAAI,MAAM,IAAI,aAAa,IAAI,CAAC;QAEtD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,KAAa,EAAE,aAAsB,KAAK;QACpE,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;QACD,OAAO,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,OAAyB;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjC,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,oBAA6B,IAAI;QACnD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAClC,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC;gBACnC,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;YAC5C,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC1C,OAAO,GAAG,SAAS,GAAG,IAAI,GAAG,OAAO,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACnD,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,UAAU;YACvB,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE;YACtC,QAAQ,EAAE,GAAG,CAAC,QAAQ;SACvB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,KAAa,EAAE,gBAAyB,KAAK;QACjE,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAE/D,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACrC,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAC9E,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAMpB,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,WAAW,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;YACrC,IAAI,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC;gBAC7B,WAAW,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC;YACzC,CAAC;QACH,CAAC;QAED,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;YACxC,cAAc;YACd,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACtG,WAAW,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;SACvD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAqC;QACxD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;CACF"}