import Conf from 'conf';
import { ConfigSchema, ConfigError } from '@/types';
import { z } from 'zod';
export class ConfigManager {
    conf;
    static instance;
    constructor() {
        this.conf = new Conf({
            projectName: 'arien-ai-cli',
            schema: {
                provider: {
                    type: 'string',
                    enum: ['deepseek', 'ollama'],
                    default: 'deepseek'
                },
                model: {
                    type: 'string',
                    default: 'deepseek-chat'
                },
                apiKey: {
                    type: 'string'
                },
                baseUrl: {
                    type: 'string'
                },
                maxTokens: {
                    type: 'number',
                    default: 4096
                },
                temperature: {
                    type: 'number',
                    default: 0.7,
                    minimum: 0,
                    maximum: 2
                },
                sessionId: {
                    type: 'string'
                },
                workingDirectory: {
                    type: 'string',
                    default: process.cwd()
                },
                autoApprove: {
                    type: 'boolean',
                    default: false
                },
                retryAttempts: {
                    type: 'number',
                    default: 3
                },
                timeout: {
                    type: 'number',
                    default: 30000
                }
            }
        });
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    get(key) {
        return this.conf.get(key);
    }
    set(key, value) {
        try {
            // Validate the value before setting by creating a partial config
            const testConfig = { ...this.getAll(), [key]: value };
            ConfigSchema.parse(testConfig);
            this.conf.set(key, value);
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigError(`Invalid value for ${key}: ${error.message}`);
            }
            throw error;
        }
    }
    getAll() {
        const config = this.conf.store;
        try {
            return ConfigSchema.parse(config);
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigError(`Invalid configuration: ${error.message}`);
            }
            throw error;
        }
    }
    setAll(config) {
        try {
            const currentConfig = this.getAll();
            const newConfig = { ...currentConfig, ...config };
            const validatedConfig = ConfigSchema.parse(newConfig);
            Object.entries(validatedConfig).forEach(([key, value]) => {
                this.conf.set(key, value);
            });
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigError(`Invalid configuration: ${error.message}`);
            }
            throw error;
        }
    }
    reset() {
        this.conf.clear();
    }
    delete(key) {
        this.conf.delete(key);
    }
    has(key) {
        return this.conf.has(key);
    }
    getConfigPath() {
        return this.conf.path;
    }
    isConfigured() {
        const config = this.getAll();
        const provider = config.provider;
        if (provider === 'deepseek') {
            return !!(config.apiKey && config.model);
        }
        else if (provider === 'ollama') {
            return !!(config.baseUrl && config.model);
        }
        return false;
    }
    validateConfig() {
        try {
            const config = this.getAll();
            ConfigSchema.parse(config);
            const errors = [];
            // Additional validation based on provider
            if (config.provider === 'deepseek' && !config.apiKey) {
                errors.push('API key is required for Deepseek provider');
            }
            if (config.provider === 'ollama' && !config.baseUrl) {
                errors.push('Base URL is required for Ollama provider');
            }
            return { valid: errors.length === 0, errors };
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                return {
                    valid: false,
                    errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
                };
            }
            return { valid: false, errors: ['Unknown configuration error'] };
        }
    }
    getProviderConfig() {
        const config = this.getAll();
        return {
            provider: config.provider,
            model: config.model,
            apiKey: config.apiKey,
            baseUrl: config.baseUrl,
            maxTokens: config.maxTokens,
            temperature: config.temperature,
            timeout: config.timeout
        };
    }
}
//# sourceMappingURL=config.js.map