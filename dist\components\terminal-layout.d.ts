import blessed from 'blessed';
import { TerminalState } from '@/types';
export interface TerminalLayoutOptions {
    title?: string;
    border?: boolean;
    scrollable?: boolean;
    mouse?: boolean;
    keys?: boolean;
}
export declare class TerminalLayout {
    private screen;
    private headerBox;
    private chatBox;
    private inputBox;
    private statusBox;
    private sidebarBox;
    private mainContainer;
    private showSidebar;
    constructor(options?: TerminalLayoutOptions);
    private setupLayout;
    private setupEventHandlers;
    private getHeaderContent;
    private getStatusContent;
    updateHeader(state: TerminalState): void;
    updateStatus(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void;
    addMessage(role: 'user' | 'assistant' | 'system' | 'tool', content: string, timestamp?: Date): void;
    clearChat(): void;
    showSpinner(message: string): void;
    hideSpinner(): void;
    toggleSidebar(): void;
    updateSidebar(sessions: any[]): void;
    private focusNext;
    private focusPrevious;
    private truncatePath;
    private handleUserInput;
    render(): void;
    destroy(): void;
    getScreen(): blessed.Widgets.Screen;
    focusInput(): void;
    setInputPlaceholder(text: string): void;
    showError(error: string): void;
    showSuccess(message: string): void;
    showWarning(message: string): void;
    showInfo(message: string): void;
}
//# sourceMappingURL=terminal-layout.d.ts.map