{"version": 3, "file": "terminal.js", "sourceRoot": "", "sources": ["../../src/components/terminal.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAE9C,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,aAAa,EAAE,MAAM,WAAW,CAAC;AAE1C,MAAM,OAAO,QAAQ;IACX,MAAM,CAAiB;IACvB,MAAM,CAAiB;IACvB,aAAa,CAAgB;IAC7B,aAAa,GAAY,KAAK,CAAC;IAEvC;QACE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC;YAC/B,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,gBAAgB;QAChB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAoB,EAAE,EAAE;YACjD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAkB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,IAAI,eAAe,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;YACtD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,sBAAsB,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE;YACjE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC1C,MAAM,OAAO,GAAG,GAAG,MAAM,SAAS,QAAQ,CAAC,QAAQ,CAAC,IAAI,iBAAiB,MAAM,CAAC,aAAa,IAAI,CAAC;YAClG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,KAAa,EAAE,EAAE;YAC/D,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;YACxC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9D,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;YACxD,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,OAAmB;QACvC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAAa;QACzC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YAAE,OAAO;QAE1B,gCAAgC;QAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,WAAW,EAAE,CAAC;YAEnB,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iDAAiD,CAAC,CAAC;gBACxE,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,CAAC;YAED,oBAAoB;YACpB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE9B,qBAAqB;YACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,+BAA+B;YAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAErB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAC/G,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,6BAA6B,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,wEAAwE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QACvH,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,OAAO,GAAG,aAAa,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAE7E,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAEhB,uCAAuC;YACvC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;YAElC,iCAAiC;YACjC,MAAM,UAAU,GAAG,IAAI,cAAc,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;YAED,wCAAwC;YACxC,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC,CAAC;gBAC/F,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;YAED,kBAAkB;YAClB,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC;gBAC/B,KAAK,EAAE,cAAc;gBACrB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;aACX,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,OAAO,GAAG,aAAa,CAAC,uBAAuB,CAAC,2BAA2B,CAAC,CAAC;QAEnF,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/B,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,uEAAuE,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAE3C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,sBAAsB,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,MAAM,CAAC,QAAQ,aAAa,MAAM,CAAC,KAAK,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QACtG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,sBAAsB,MAAM,CAAC,gBAAgB,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAC9F,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,0CAA0C,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QACzF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,mCAAmC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,4BAA4B,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAC3E,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,8BAA8B,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAC7E,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,gCAAgC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,sCAAsC,CAAC,CAAC;IACnE,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,2CAA2C;QAC3C,IAAI,CAAC;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;YAEnB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gDAAgD,CAAC,CAAC;gBAEvE,gEAAgE;gBAChE,MAAM,UAAU,GAAG,IAAI,cAAc,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;gBAE7C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;oBACzE,OAAO;gBACT,CAAC;YACH,CAAC;YAED,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAErB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,OAAO;QAClB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,4BAA4B,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACvG,CAAC;QACH,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,6BAA6B,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,yBAAyB;YACzB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;YAElC,iBAAiB;YACjB,MAAM,UAAU,GAAG,IAAI,cAAc,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;YAExC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,8BAA8B;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC;oBAC/B,KAAK,EAAE,cAAc;oBACrB,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,IAAI;iBACX,CAAC,CAAC;gBAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAExB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IACrD,CAAC;IAEM,OAAO;QACZ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC;QAED,aAAa,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAED,uCAAuC;IAChC,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEM,gBAAgB,CAAC,OAAe;QACrC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;IACxD,CAAC;IAEM,UAAU,CAAC,OAAe,EAAE,OAAiD,MAAM;QACxF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;CACF"}