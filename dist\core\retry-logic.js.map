{"version": 3, "file": "retry-logic.js", "sourceRoot": "", "sources": ["../../src/core/retry-logic.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,MAAM,OAAO,CAAC;AAsB1B,MAAM,OAAO,UAAW,SAAQ,YAAY;IAClC,cAAc,CAA6D;IAEnF;QACE,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,cAAc,GAAG;YACpB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,MAAM,CAAC,YAAY;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAC3B,EAAoB,EACpB,UAAwB,EAAE;QAE1B,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QACpD,MAAM,OAAO,GAAiB;YAC5B,OAAO,EAAE,CAAC;YACV,aAAa,EAAE,IAAI,CAAC,WAAW;YAC/B,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,IAAI;SAClB,CAAC;QAEF,IAAI,OAAO,GAAQ,IAAI,CAAC;QAExB,IAAI,CAAC;YACH,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;gBAC7D,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;gBAE1B,IAAI,CAAC;oBACH,sCAAsC;oBACtC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;wBAChB,OAAO,GAAG,aAAa,CAAC,kBAAkB,CACxC,kCAAkC,OAAO,GAAG,CAC7C,CAAC;wBACF,OAAO,CAAC,KAAK,EAAE,CAAC;wBAEhB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;wBAEjD,2CAA2C;wBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;wBACrD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAE3B,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC1B,CAAC;oBAED,oCAAoC;oBACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBAE/D,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO,CAAC,OAAO,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;oBAC/D,CAAC;oBAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;oBACzD,OAAO,MAAM,CAAC;gBAEhB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,SAAS,GAAG,KAAc,CAAC;oBAEnC,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO,CAAC,IAAI,EAAE,CAAC;oBACjB,CAAC;oBAED,2BAA2B;oBAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBACpE,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;oBAElC,IAAI,CAAC,WAAW,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjD,IAAI,OAAO,EAAE,CAAC;4BACZ,OAAO,CAAC,IAAI,CAAC,0BAA0B,OAAO,WAAW,CAAC,CAAC;wBAC7D,CAAC;wBAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;wBACvD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,kCAAkC;oBAClC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;wBACpB,OAAO,CAAC,OAAO,CAAC,KAAc,EAAE,OAAO,CAAC,CAAC;oBAC3C,CAAC;oBAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;YAED,2DAA2D;YAC3D,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAE9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACnC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,EAAoB,EACpB,OAAe;QAEf,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,OAAO,IAAI,CAAC,CAAC,CAAC;YAC9D,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,EAAE,EAAE;iBACD,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe,EAAE,OAAmE;QACzG,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAEzE,4BAA4B;QAC5B,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,wCAAwC;QACxC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAY,EAAE,OAAe,EAAE,OAAqB;QACtE,yCAAyC;QACzC,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,OAAO,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChD,CAAC;QAED,2BAA2B;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAY;QACnC,MAAM,iBAAiB,GAAG;YACxB,iBAAiB;YACjB,aAAa;YACb,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,iBAAiB;YACjB,kBAAkB;YAElB,cAAc;YACd,kBAAkB;YAClB,0BAA0B;YAC1B,sBAAsB;YACtB,wBAAwB;YAExB,gBAAgB;YAChB,aAAa;YACb,iBAAiB;YACjB,YAAY;YAEZ,qBAAqB;YACrB,YAAY;YACZ,0BAA0B;YAC1B,YAAY;YAEZ,2BAA2B;YAC3B,mBAAmB;YACnB,oBAAoB;YACpB,WAAW;SACZ,CAAC;QAEF,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;QACvD,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,0BAA0B;QACtC,OAAO;YACL,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,IAAI;YACZ,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE;gBAC/B,MAAM,aAAa,GAAG;oBACpB,aAAa;oBACb,eAAe;oBACf,YAAY;oBACZ,YAAY;oBACZ,iBAAiB;oBACjB,kBAAkB;iBACnB,CAAC;gBAEF,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACpE,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,4BAA4B;QACxC,OAAO;YACL,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,GAAG;YAClB,MAAM,EAAE,IAAI;YACZ,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE;gBAC/B,MAAM,eAAe,GAAG;oBACtB,MAAM;oBACN,aAAa;oBACb,iBAAiB;oBACjB,YAAY;oBACZ,oBAAoB;iBACrB,CAAC;gBAEF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtE,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,8BAA8B;QAC1C,OAAO;YACL,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,IAAI;YACZ,cAAc,EAAE,CAAC,KAAY,EAAE,OAAe,EAAE,EAAE;gBAChD,uCAAuC;gBACvC,IAAI,iCAAiC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC1D,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,sCAAsC;gBACtC,IAAI,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;oBACnD,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,yCAAyC;gBACzC,MAAM,eAAe,GAAG;oBACtB,QAAQ,EAAE,aAAa;oBACvB,MAAM,EAAI,aAAa;oBACvB,aAAa;oBACb,aAAa;oBACb,WAAW;oBACX,UAAU;oBACV,YAAY;iBACb,CAAC;gBAEF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtE,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,+BAA+B;QAC3C,OAAO;YACL,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,KAAK;YACb,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE;gBAC/B,4CAA4C;gBAC5C,MAAM,eAAe,GAAG;oBACtB,mCAAmC;oBACnC,0BAA0B;oBAC1B,sBAAsB;oBACtB,qBAAqB;iBACtB,CAAC;gBAEF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtE,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,sBAAsB,CACxC,EAAoB,EACpB,cAAsB,CAAC,EACvB,YAAoB,IAAI;QAExB,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,OAAO,UAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACrC,WAAW;YACX,SAAS;YACT,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CACnC,EAAoB,EACpB,cAAsB,CAAC,EACvB,QAAgB,IAAI;QAEpB,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,OAAO,UAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACrC,WAAW;YACX,SAAS,EAAE,KAAK;YAChB,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CACrC,EAAoB,EACpB,cAA0D,EAC1D,cAAsB,CAAC;QAEvB,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,OAAO,UAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACrC,WAAW;YACX,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB,CAAC,OAAqB;QACpD,MAAM,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACpE,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1C,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAExE,OAAO;YACL,KAAK,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAClE,KAAK,CAAC,MAAM,CAAC,aAAa,QAAQ,IAAI,CAAC;YACvC,KAAK,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;YAClD,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;SAC/E,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;CACF"}