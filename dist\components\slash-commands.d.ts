import { SlashCommand } from '@/types';
import { ConfigManager } from '@/core/config';
import { SessionManager } from '@/core/session-manager';
import { LLMManager } from '@/core/llm-manager';
export declare class SlashCommandManager {
    private commands;
    private configManager;
    private sessionManager;
    private llmManager;
    constructor(configManager: ConfigManager, sessionManager: SessionManager, llmManager: LLMManager);
    private registerDefaultCommands;
    registerCommand(command: SlashCommand): void;
    executeCommand(input: string): Promise<boolean>;
    getCommands(): SlashCommand[];
    private showCurrentModel;
    private listAvailableModels;
    private switchModel;
    private showCurrentProvider;
    private switchProvider;
    private createNewSession;
    private listSessions;
    private switchSession;
    private deleteSession;
    private exportSession;
    private clearCurrentSession;
    private showCurrentSession;
    private showMessageHistory;
    private showConfig;
    private showConfigValue;
    private setConfigValue;
    private showHelp;
    private showCommandHelp;
    private showStatus;
    private showWorkingDirectory;
    private changeWorkingDirectory;
}
//# sourceMappingURL=slash-commands.d.ts.map