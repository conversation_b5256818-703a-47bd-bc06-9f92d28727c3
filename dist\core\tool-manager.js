import { CommandError } from '@/types';
import { ShellTool } from '@/tools/shell';
import { nanoid } from 'nanoid';
export class ToolManager {
    tools = new Map();
    shellTool;
    constructor(workingDirectory) {
        this.shellTool = new ShellTool(workingDirectory);
        this.registerDefaultTools();
    }
    registerDefaultTools() {
        this.registerTool('execute_shell_command', this.shellTool);
    }
    registerTool(name, tool) {
        this.tools.set(name, tool);
    }
    getToolDefinitions() {
        const definitions = [];
        this.tools.forEach((tool, name) => {
            if (typeof tool.getToolDefinition === 'function') {
                definitions.push(tool.getToolDefinition());
            }
        });
        return definitions;
    }
    getToolDefinition(name) {
        const tool = this.tools.get(name);
        if (tool && typeof tool.getToolDefinition === 'function') {
            return tool.getToolDefinition();
        }
        return null;
    }
    async executeToolCall(toolCall) {
        const startTime = Date.now();
        const toolName = toolCall.function.name;
        const tool = this.tools.get(toolName);
        if (!tool) {
            return {
                toolCallId: toolCall.id,
                result: `Error: Tool '${toolName}' not found`,
                success: false,
                error: `Tool '${toolName}' is not registered`,
                executionTime: Date.now() - startTime
            };
        }
        try {
            let args;
            try {
                args = JSON.parse(toolCall.function.arguments);
            }
            catch (parseError) {
                return {
                    toolCallId: toolCall.id,
                    result: `Error: Invalid JSON arguments for tool '${toolName}'`,
                    success: false,
                    error: `Failed to parse arguments: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`,
                    executionTime: Date.now() - startTime
                };
            }
            let result;
            // Handle different tool types
            if (toolName === 'execute_shell_command') {
                result = await this.executeShellCommand(args);
            }
            else if (typeof tool.execute === 'function') {
                result = await tool.execute(args);
            }
            else {
                throw new Error(`Tool '${toolName}' does not have an execute method`);
            }
            return {
                toolCallId: toolCall.id,
                result: this.formatToolResult(result),
                success: true,
                executionTime: Date.now() - startTime
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                toolCallId: toolCall.id,
                result: `Error executing tool '${toolName}': ${errorMessage}`,
                success: false,
                error: errorMessage,
                executionTime: Date.now() - startTime
            };
        }
    }
    async executeShellCommand(args) {
        const { command, workingDirectory, timeout, requireApproval } = args;
        if (!command) {
            throw new CommandError('Command is required');
        }
        // Execute the shell command
        const result = await this.shellTool.execute(command, {
            workingDirectory,
            timeout,
            requireApproval
        });
        return result;
    }
    formatToolResult(result) {
        if (typeof result === 'string') {
            return result;
        }
        if (result && typeof result === 'object') {
            // Handle shell command results
            if ('stdout' in result && 'stderr' in result && 'exitCode' in result) {
                const output = [];
                if (result.stdout) {
                    output.push(`STDOUT:\n${result.stdout}`);
                }
                if (result.stderr) {
                    output.push(`STDERR:\n${result.stderr}`);
                }
                output.push(`Exit Code: ${result.exitCode}`);
                output.push(`Execution Time: ${result.executionTime}ms`);
                output.push(`Success: ${result.success}`);
                return output.join('\n\n');
            }
            // For other object types, stringify them
            try {
                return JSON.stringify(result, null, 2);
            }
            catch {
                return String(result);
            }
        }
        return String(result);
    }
    async executeToolCalls(toolCalls) {
        const results = [];
        // Execute tool calls sequentially to maintain order and handle dependencies
        for (const toolCall of toolCalls) {
            const result = await this.executeToolCall(toolCall);
            results.push(result);
        }
        return results;
    }
    createToolResultMessages(toolResults) {
        return toolResults.map(result => ({
            id: nanoid(),
            role: 'tool',
            content: result.result,
            toolCallId: result.toolCallId,
            timestamp: new Date()
        }));
    }
    setWorkingDirectory(directory) {
        this.shellTool.setWorkingDirectory(directory);
    }
    getWorkingDirectory() {
        return this.shellTool.getWorkingDirectory();
    }
    async validateWorkingDirectory(directory) {
        return await this.shellTool.validateDirectory(directory);
    }
    async getCurrentDirectory() {
        return await this.shellTool.getCurrentDirectory();
    }
    getAvailableTools() {
        return Array.from(this.tools.keys());
    }
    hasToolCalls(message) {
        return !!(message.toolCalls && message.toolCalls.length > 0);
    }
    extractToolCalls(message) {
        return message.toolCalls || [];
    }
    async processMessageWithTools(message, onToolExecution) {
        const resultMessages = [];
        if (this.hasToolCalls(message)) {
            const toolCalls = this.extractToolCalls(message);
            for (const toolCall of toolCalls) {
                const result = await this.executeToolCall(toolCall);
                // Call the callback if provided
                if (onToolExecution) {
                    onToolExecution(toolCall, result);
                }
                // Create tool result message
                const toolResultMessage = {
                    id: nanoid(),
                    role: 'tool',
                    content: result.result,
                    toolCallId: result.toolCallId,
                    timestamp: new Date()
                };
                resultMessages.push(toolResultMessage);
            }
        }
        return resultMessages;
    }
    getToolUsageStats() {
        // This would be implemented with proper tracking in a real application
        return {
            totalExecutions: 0,
            toolUsage: {},
            averageExecutionTime: 0
        };
    }
}
//# sourceMappingURL=tool-manager.js.map