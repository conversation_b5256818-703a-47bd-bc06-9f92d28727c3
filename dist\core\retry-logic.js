import { EventEmitter } from 'events';
import { ModernSpinner } from '@/components/spinner';
import chalk from 'chalk';
export class RetryLogic extends EventEmitter {
    defaultOptions;
    constructor() {
        super();
        this.defaultOptions = {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 2,
            jitter: true,
            timeout: 300000 // 5 minutes
        };
    }
    /**
     * Execute function with retry logic
     */
    async executeWithRetry(fn, options = {}) {
        const opts = { ...this.defaultOptions, ...options };
        const context = {
            attempt: 0,
            totalAttempts: opts.maxAttempts,
            lastError: null,
            startTime: new Date(),
            delays: [],
            isRetryable: true
        };
        let spinner = null;
        try {
            for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
                context.attempt = attempt;
                try {
                    // Show retry spinner for attempts > 1
                    if (attempt > 1) {
                        spinner = ModernSpinner.createRetrySpinner(`Retrying operation... (attempt ${attempt})`);
                        spinner.start();
                        this.emit('retry-attempt', { context, attempt });
                        // Calculate delay with exponential backoff
                        const delay = this.calculateDelay(attempt - 1, opts);
                        context.delays.push(delay);
                        await this.delay(delay);
                    }
                    // Execute the function with timeout
                    const result = await this.executeWithTimeout(fn, opts.timeout);
                    if (spinner) {
                        spinner.succeed(`Operation succeeded on attempt ${attempt}`);
                    }
                    this.emit('retry-success', { context, result, attempt });
                    return result;
                }
                catch (error) {
                    context.lastError = error;
                    if (spinner) {
                        spinner.stop();
                    }
                    // Check if we should retry
                    const shouldRetry = this.shouldRetry(error, attempt, opts);
                    context.isRetryable = shouldRetry;
                    if (!shouldRetry || attempt === opts.maxAttempts) {
                        if (spinner) {
                            spinner.fail(`Operation failed after ${attempt} attempts`);
                        }
                        this.emit('retry-failed', { context, error, attempt });
                        throw error;
                    }
                    // Call retry callback if provided
                    if (options.onRetry) {
                        options.onRetry(error, attempt);
                    }
                    this.emit('retry-error', { context, error, attempt });
                }
            }
            // This should never be reached, but TypeScript requires it
            throw context.lastError || new Error('Unknown retry error');
        }
        catch (error) {
            if (spinner && spinner.isSpinning) {
                spinner.fail('Operation failed');
            }
            throw error;
        }
    }
    /**
     * Execute function with timeout
     */
    async executeWithTimeout(fn, timeout) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Operation timed out after ${timeout}ms`));
            }, timeout);
            fn()
                .then(result => {
                clearTimeout(timeoutId);
                resolve(result);
            })
                .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
    /**
     * Calculate delay with exponential backoff and jitter
     */
    calculateDelay(attempt, options) {
        let delay = options.baseDelay * Math.pow(options.backoffFactor, attempt);
        // Apply maximum delay limit
        delay = Math.min(delay, options.maxDelay);
        // Add jitter to prevent thundering herd
        if (options.jitter) {
            delay = delay * (0.5 + Math.random() * 0.5);
        }
        return Math.floor(delay);
    }
    /**
     * Determine if error should trigger a retry
     */
    shouldRetry(error, attempt, options) {
        // Use custom retry condition if provided
        if (options.retryCondition) {
            return options.retryCondition(error, attempt);
        }
        // Default retry conditions
        return this.isRetryableError(error);
    }
    /**
     * Check if error is retryable by default
     */
    isRetryableError(error) {
        const retryablePatterns = [
            // Network errors
            /ECONNRESET/i,
            /ECONNREFUSED/i,
            /ETIMEDOUT/i,
            /ENOTFOUND/i,
            /socket hang up/i,
            /network timeout/i,
            // HTTP errors
            /502 Bad Gateway/i,
            /503 Service Unavailable/i,
            /504 Gateway Timeout/i,
            /429 Too Many Requests/i,
            // Rate limiting
            /rate limit/i,
            /quota exceeded/i,
            /throttled/i,
            // Temporary failures
            /temporary/i,
            /temporarily unavailable/i,
            /try again/i,
            // Provider-specific errors
            /model overloaded/i,
            /server overloaded/i,
            /capacity/i
        ];
        const errorMessage = error.message || error.toString();
        return retryablePatterns.some(pattern => pattern.test(errorMessage));
    }
    /**
     * Simple delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Create retry strategy for specific scenarios
     */
    static createNetworkRetryStrategy() {
        return {
            maxAttempts: 5,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 2,
            jitter: true,
            retryCondition: (error) => {
                const networkErrors = [
                    /ECONNRESET/i,
                    /ECONNREFUSED/i,
                    /ETIMEDOUT/i,
                    /ENOTFOUND/i,
                    /socket hang up/i,
                    /network timeout/i
                ];
                return networkErrors.some(pattern => pattern.test(error.message));
            }
        };
    }
    /**
     * Create retry strategy for rate limiting
     */
    static createRateLimitRetryStrategy() {
        return {
            maxAttempts: 10,
            baseDelay: 2000,
            maxDelay: 60000,
            backoffFactor: 1.5,
            jitter: true,
            retryCondition: (error) => {
                const rateLimitErrors = [
                    /429/i,
                    /rate limit/i,
                    /quota exceeded/i,
                    /throttled/i,
                    /too many requests/i
                ];
                return rateLimitErrors.some(pattern => pattern.test(error.message));
            }
        };
    }
    /**
     * Create retry strategy for LLM provider errors
     */
    static createLLMProviderRetryStrategy() {
        return {
            maxAttempts: 3,
            baseDelay: 2000,
            maxDelay: 15000,
            backoffFactor: 2,
            jitter: true,
            retryCondition: (error, attempt) => {
                // Don't retry on authentication errors
                if (/401|403|unauthorized|forbidden/i.test(error.message)) {
                    return false;
                }
                // Don't retry on invalid input errors
                if (/400|bad request|invalid/i.test(error.message)) {
                    return false;
                }
                // Retry on server errors and rate limits
                const retryableErrors = [
                    /5\d\d/i, // 5xx errors
                    /429/i, // Rate limit
                    /rate limit/i,
                    /overloaded/i,
                    /capacity/i,
                    /timeout/i,
                    /temporary/i
                ];
                return retryableErrors.some(pattern => pattern.test(error.message));
            }
        };
    }
    /**
     * Create retry strategy for shell commands
     */
    static createShellCommandRetryStrategy() {
        return {
            maxAttempts: 2,
            baseDelay: 500,
            maxDelay: 5000,
            backoffFactor: 2,
            jitter: false,
            retryCondition: (error) => {
                // Only retry on specific temporary failures
                const retryableErrors = [
                    /resource temporarily unavailable/i,
                    /device or resource busy/i,
                    /operation timed out/i,
                    /connection refused/i
                ];
                return retryableErrors.some(pattern => pattern.test(error.message));
            }
        };
    }
    /**
     * Retry with exponential backoff (utility function)
     */
    static async withExponentialBackoff(fn, maxAttempts = 3, baseDelay = 1000) {
        const retryLogic = new RetryLogic();
        return retryLogic.executeWithRetry(fn, {
            maxAttempts,
            baseDelay,
            backoffFactor: 2,
            jitter: true
        });
    }
    /**
     * Retry with linear backoff (utility function)
     */
    static async withLinearBackoff(fn, maxAttempts = 3, delay = 1000) {
        const retryLogic = new RetryLogic();
        return retryLogic.executeWithRetry(fn, {
            maxAttempts,
            baseDelay: delay,
            backoffFactor: 1,
            jitter: false
        });
    }
    /**
     * Retry with custom condition (utility function)
     */
    static async withCustomCondition(fn, retryCondition, maxAttempts = 3) {
        const retryLogic = new RetryLogic();
        return retryLogic.executeWithRetry(fn, {
            maxAttempts,
            retryCondition
        });
    }
    /**
     * Format retry context for logging
     */
    static formatRetryContext(context) {
        const duration = new Date().getTime() - context.startTime.getTime();
        const avgDelay = context.delays.length > 0 ?
            context.delays.reduce((a, b) => a + b, 0) / context.delays.length : 0;
        return [
            chalk.blue(`Attempt: ${context.attempt}/${context.totalAttempts}`),
            chalk.yellow(`Duration: ${duration}ms`),
            chalk.gray(`Avg Delay: ${Math.round(avgDelay)}ms`),
            context.lastError ? chalk.red(`Last Error: ${context.lastError.message}`) : ''
        ].filter(Boolean).join(' | ');
    }
}
//# sourceMappingURL=retry-logic.js.map