{"version": 3, "file": "error-handler.js", "sourceRoot": "", "sources": ["../../src/core/error-handler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAc,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAC/E,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AA8BlC,MAAM,OAAO,YAAa,SAAQ,YAAY;IACpC,OAAO,CAAgC;IACvC,YAAY,GAAkB,EAAE,CAAC;IACjC,UAAU,CAAa;IAE/B,YAAY,UAA+B,EAAE;QAC3C,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,OAAO,GAAG;YACb,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;YACjB,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,OAAO;YACjB,eAAe,EAAE,GAAG;YACpB,GAAG,OAAO;SACX,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,6BAA6B;QAC7B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;YACxC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;gBACnB,SAAS,EAAE,SAAS;aACrB,EAAE,UAAU,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YACnD,MAAM,KAAK,GAAG,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC3E,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,SAAS;aACrB,EAAE,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,KAAY,EACZ,OAAqB,EACrB,WAAoC,QAAQ;QAE5C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEhE,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,YAAY;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAEnC,yBAAyB;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACxC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CACvB,KAAY,EACZ,OAAqB,EACrB,QAAiC;QAEjC,MAAM,MAAM,GAAgB;YAC1B,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;YAC1B,KAAK;YACL,OAAO;YACP,QAAQ;YACR,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YACrC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YACzC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;YACtD,UAAU,EAAE,KAAK,CAAC,KAAK;SACxB,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAY;QAClC,IAAI,KAAK,YAAY,aAAa,EAAE,CAAC;YACnC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,KAAK,YAAY,YAAY,EAAE,CAAC;YAClC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,+BAA+B;QAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,IAAI,wCAAwC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3D,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,2CAA2C,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,8BAA8B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACjD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAY;QACnC,MAAM,iBAAiB,GAAG;YACxB,UAAU;YACV,UAAU;YACV,aAAa;YACb,YAAY;YACZ,aAAa;YACb,cAAc;SACf,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,KAAY;QAC3C,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,8BAA8B;QAC9B,IAAI,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7C,CAAC;QAED,+BAA+B;QAC/B,IAAI,KAAK,YAAY,aAAa,IAAI,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,4BAA4B;QAC5B,IAAI,KAAK,YAAY,WAAW,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACpE,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAChD,CAAC;QAED,gCAAgC;QAChC,IAAI,KAAK,YAAY,YAAY,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACtD,CAAC;QAED,4BAA4B;QAC5B,IAAI,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,mBAAmB;QACnB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,MAAmB;QAClC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QAElE,MAAM,MAAM,GAAG,GAAG,SAAS,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;QACjE,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACvE,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAE5D,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACtB,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC,CAAC;QAChC,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC,CAAC;QAChC,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC;QAE9B,yBAAyB;QACzB,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,MAAM,EAAE,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACxE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC5C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjG,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;IAClD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAAiC;QACtD,MAAM,cAAc,GAAG;YACrB,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;YACvB,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC9B,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;YACvB,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC;SACxC,CAAC;QAEF,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,MAAmB;QACpC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YACvC,OAAO,EAAE,MAAM,CAAC,gBAAgB;SACjC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAmB;QAC3C,MAAM,gBAAgB,GAAG;YACvB,OAAO,EAAE,0BAA0B;YACnC,QAAQ,EAAE,mBAAmB;YAC7B,OAAO,EAAE,0BAA0B;YACnC,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,cAAc;YACtB,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,kBAAkB;SAC5B,CAAC;QAEF,MAAM,eAAe,GAAG,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1D,OAAO,GAAG,eAAe,KAAK,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAAmB;QACtC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAElC,qBAAqB;QACrB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,wBAAwB,CACnC,SAA2B,EAC3B,OAAwC,EACxC,UAGI,EAAE;QAEN,MAAM,WAAW,GAAiB;YAChC,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACrD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;YACjF,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CACnC,KAAc,EACd,WAAW,EACX,OAAO,CAAC,QAAQ,CACjB,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB;QAOvB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE5D,MAAM,gBAAgB,GAA2B,EAAE,CAAC;QACpD,MAAM,gBAAgB,GAA2B,EAAE,CAAC;QACpD,MAAM,aAAa,GAA2B,EAAE,CAAC;QAEjD,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACvC,oBAAoB;YACpB,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAEjF,oBAAoB;YACpB,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAEjF,sBAAsB;YACtB,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;gBAC1C,YAAY,EAAE,CAAC;YACjB,CAAC;YAED,uBAAuB;YACvB,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YACrC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7D,CAAC;QAED,yBAAyB;QACzB,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;aAClD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QAEjD,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACrC,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAgB,EAAE;QACvC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACrC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACvC,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE;gBACjD,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS;gBACnC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS;gBACnC,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;gBAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;aAC1C,CAAC,CAAC;SACJ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,SAAiB;QAC7C,OAAO;YACL,WAAW,EAAE,CAAC,KAAY,EAAE,SAAiB,EAAE,QAAkC,EAAE,EAAE;gBACnF,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;oBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,SAAS;oBACT,SAAS;iBACV,EAAE,QAAQ,CAAC,CAAC;YACf,CAAC;YAED,mBAAmB,EAAE,CACnB,SAA2B,EAC3B,aAAqB,EACrB,OAAa,EACb,EAAE;gBACF,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE;oBAC9C,SAAS,EAAE,aAAa;oBACxB,SAAS;iBACV,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;SACF,CAAC;IACJ,CAAC;CACF"}