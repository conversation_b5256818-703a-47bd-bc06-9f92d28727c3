import { Config } from '@/types';
export declare class ConfigManager {
    private conf;
    private static instance;
    private constructor();
    static getInstance(): ConfigManager;
    get<K extends keyof Config>(key: K): Config[K];
    set<K extends keyof Config>(key: K, value: Config[K]): void;
    getAll(): Config;
    setAll(config: Partial<Config>): void;
    reset(): void;
    delete<K extends keyof Config>(key: K): void;
    has<K extends keyof Config>(key: K): boolean;
    getConfigPath(): string;
    isConfigured(): boolean;
    validateConfig(): {
        valid: boolean;
        errors: string[];
    };
    getProviderConfig(): {
        provider: "deepseek" | "ollama";
        model: string;
        apiKey: string | undefined;
        baseUrl: string | undefined;
        maxTokens: number;
        temperature: number;
        timeout: number;
    };
}
//# sourceMappingURL=config.d.ts.map