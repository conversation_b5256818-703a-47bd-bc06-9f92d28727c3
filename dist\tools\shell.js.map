{"version": 3, "file": "shell.js", "sourceRoot": "", "sources": ["../../src/tools/shell.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC9B,OAAO,EAAsC,YAAY,EAAE,MAAM,SAAS,CAAC;AAC3E,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAG9B,MAAM,OAAO,SAAS;IACZ,gBAAgB,CAAS;IAChB,gBAAgB,GAAG,MAAM,CAAC,CAAC,YAAY;IACvC,iBAAiB,GAAG;QACnC,UAAU;QACV,mBAAmB;QACnB,QAAQ;QACR,OAAO;QACP,MAAM;QACN,iBAAiB;QACjB,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,oBAAoB;QACpB,kBAAkB;KACnB,CAAC;IAEF,YAAY,mBAA2B,OAAO,CAAC,GAAG,EAAE;QAClD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAEM,iBAAiB;QACtB,OAAO;YACL,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAoC+C;YAC5D,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,gFAAgF;qBAC9F;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,kFAAkF;qBAChG;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,mEAAmE;qBACjF;oBACD,eAAe,EAAE;wBACf,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,mFAAmF;qBACjG;iBACF;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACtB;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,yFAAyF;gBAC/F,OAAO,EAAE,6FAA6F;gBACtG,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE;oBACR,+CAA+C;oBAC/C,mDAAmD;oBACnD,oCAAoC;oBACpC,0CAA0C;oBAC1C,6CAA6C;oBAC7C,0BAA0B;oBAC1B,sDAAsD;iBACvD;aACF;SACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAClB,OAAe,EACf,UAII,EAAE;QAEN,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC9D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAEzD,IAAI,CAAC;YACH,0BAA0B;YAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9B,iDAAiD;YACjD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAElD,kBAAkB;YAClB,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;gBACrC,GAAG;gBACH,OAAO;gBACP,MAAM,EAAE,KAAK;gBACb,iBAAiB,EAAE,KAAK;gBACxB,QAAQ,EAAE,MAAM;gBAChB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,oCAAoC;aAC/E,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;gBAC9B,OAAO;gBACP,aAAa;gBACb,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,CAAC;aAC/B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,MAAM,IAAI,YAAY,CACpB,2BAA2B,OAAO,OAAO,OAAO,EAAE,EAClD,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,CACpC,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;gBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,eAAe;gBACxD,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,CAAC;gBAC7B,OAAO;gBACP,aAAa;gBACb,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,OAAe;QACrC,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAEvD,+BAA+B;QAC/B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/C,IAAI,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACxD,MAAM,IAAI,YAAY,CACpB,2CAA2C,OAAO,wCAAwC,EAC1F,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,CACzC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YACpB,MAAM,IAAI,YAAY,CAAC,wBAAwB,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,OAAe;QAClC,MAAM,SAAS,GAAG,QAAQ,EAAE,KAAK,OAAO,CAAC;QAEzC,IAAI,SAAS,EAAE,CAAC;YACd,8CAA8C;YAC9C,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,+BAA+B;YAC/B,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,mBAAmB,CAAC,SAAiB;QAC1C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC9C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,SAAS,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/E,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5D,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACvE,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;IACH,CAAC;CACF"}