import Conf from 'conf';
import { Config, ConfigSchema, ConfigError } from '@/types';
import { z } from 'zod';

export class ConfigManager {
  private conf: Conf<Config>;
  private static instance: ConfigManager;

  private constructor() {
    this.conf = new Conf<Config>({
      projectName: 'arien-ai-cli',
      schema: {
        provider: {
          type: 'string',
          enum: ['deepseek', 'ollama'],
          default: 'deepseek'
        },
        model: {
          type: 'string',
          default: 'deepseek-chat'
        },
        apiKey: {
          type: 'string'
        },
        baseUrl: {
          type: 'string'
        },
        maxTokens: {
          type: 'number',
          default: 4096
        },
        temperature: {
          type: 'number',
          default: 0.7,
          minimum: 0,
          maximum: 2
        },
        sessionId: {
          type: 'string'
        },
        workingDirectory: {
          type: 'string',
          default: process.cwd()
        },
        autoApprove: {
          type: 'boolean',
          default: false
        },
        retryAttempts: {
          type: 'number',
          default: 3
        },
        timeout: {
          type: 'number',
          default: 30000
        }
      }
    });
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  public get<K extends keyof Config>(key: K): Config[K] {
    return this.conf.get(key);
  }

  public set<K extends keyof Config>(key: K, value: Config[K]): void {
    try {
      // Validate the value before setting by creating a partial config
      const testConfig = { ...this.getAll(), [key]: value };
      ConfigSchema.parse(testConfig);
      this.conf.set(key, value);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ConfigError(`Invalid value for ${key}: ${error.message}`);
      }
      throw error;
    }
  }

  public getAll(): Config {
    const config = this.conf.store;
    try {
      return ConfigSchema.parse(config);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ConfigError(`Invalid configuration: ${error.message}`);
      }
      throw error;
    }
  }

  public setAll(config: Partial<Config>): void {
    try {
      const currentConfig = this.getAll();
      const newConfig = { ...currentConfig, ...config };
      const validatedConfig = ConfigSchema.parse(newConfig);
      
      Object.entries(validatedConfig).forEach(([key, value]) => {
        this.conf.set(key as keyof Config, value);
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ConfigError(`Invalid configuration: ${error.message}`);
      }
      throw error;
    }
  }

  public reset(): void {
    this.conf.clear();
  }

  public delete<K extends keyof Config>(key: K): void {
    this.conf.delete(key);
  }

  public has<K extends keyof Config>(key: K): boolean {
    return this.conf.has(key);
  }

  public getConfigPath(): string {
    return this.conf.path;
  }

  public isConfigured(): boolean {
    const config = this.getAll();
    const provider = config.provider;
    
    if (provider === 'deepseek') {
      return !!(config.apiKey && config.model);
    } else if (provider === 'ollama') {
      return !!(config.baseUrl && config.model);
    }
    
    return false;
  }

  public validateConfig(): { valid: boolean; errors: string[] } {
    try {
      const config = this.getAll();
      ConfigSchema.parse(config);
      
      const errors: string[] = [];
      
      // Additional validation based on provider
      if (config.provider === 'deepseek' && !config.apiKey) {
        errors.push('API key is required for Deepseek provider');
      }
      
      if (config.provider === 'ollama' && !config.baseUrl) {
        errors.push('Base URL is required for Ollama provider');
      }
      
      return { valid: errors.length === 0, errors };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        };
      }
      return { valid: false, errors: ['Unknown configuration error'] };
    }
  }

  public getProviderConfig() {
    const config = this.getAll();
    return {
      provider: config.provider,
      model: config.model,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      timeout: config.timeout
    };
  }
}
