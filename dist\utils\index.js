import { platform } from 'os';
import { resolve, join } from 'path';
/**
 * Utility functions for the Arien AI CLI
 */
// Platform detection
export const isWindows = () => platform() === 'win32';
export const isMacOS = () => platform() === 'darwin';
export const isLinux = () => platform() === 'linux';
export const isWSL = () => {
    try {
        const fs = require('fs');
        return fs.existsSync('/proc/version') &&
            fs.readFileSync('/proc/version', 'utf8').toLowerCase().includes('microsoft');
    }
    catch {
        return false;
    }
};
// String utilities
export const truncateString = (str, maxLength, suffix = '...') => {
    if (str.length <= maxLength)
        return str;
    return str.slice(0, maxLength - suffix.length) + suffix;
};
export const capitalizeFirst = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
};
export const camelToKebab = (str) => {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
};
export const kebabToCamel = (str) => {
    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
};
// Time utilities
export const formatDuration = (ms) => {
    if (ms < 1000)
        return `${ms}ms`;
    if (ms < 60000)
        return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000)
        return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
};
export const formatTimestamp = (date) => {
    return date.toLocaleString();
};
export const formatRelativeTime = (date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    if (diff < 60000)
        return 'just now';
    if (diff < 3600000)
        return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000)
        return `${Math.floor(diff / 3600000)}h ago`;
    if (diff < 604800000)
        return `${Math.floor(diff / 86400000)}d ago`;
    return date.toLocaleDateString();
};
// File and path utilities
export const resolvePath = (path, basePath) => {
    if (path.startsWith('~')) {
        return resolve(require('os').homedir(), path.slice(1));
    }
    if (basePath) {
        return resolve(basePath, path);
    }
    return resolve(path);
};
export const getHomeDirectory = () => {
    return require('os').homedir();
};
export const getConfigDirectory = () => {
    const home = getHomeDirectory();
    if (isWindows()) {
        return join(process.env.APPDATA || join(home, 'AppData', 'Roaming'), 'arien-ai-cli');
    }
    else {
        return join(home, '.config', 'arien-ai-cli');
    }
};
export const getDataDirectory = () => {
    const home = getHomeDirectory();
    if (isWindows()) {
        return join(process.env.LOCALAPPDATA || join(home, 'AppData', 'Local'), 'arien-ai-cli');
    }
    else if (isMacOS()) {
        return join(home, 'Library', 'Application Support', 'arien-ai-cli');
    }
    else {
        return join(home, '.local', 'share', 'arien-ai-cli');
    }
};
// Validation utilities
export const isValidUrl = (url) => {
    try {
        new URL(url);
        return true;
    }
    catch {
        return false;
    }
};
export const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
export const isValidApiKey = (apiKey, provider) => {
    if (!apiKey || typeof apiKey !== 'string')
        return false;
    switch (provider) {
        case 'deepseek':
            return apiKey.startsWith('sk-') && apiKey.length > 10;
        case 'ollama':
            return true; // Ollama doesn't require API keys
        default:
            return apiKey.length > 0;
    }
};
// Array utilities
export const chunk = (array, size) => {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
};
export const unique = (array) => {
    return [...new Set(array)];
};
export const groupBy = (array, keyFn) => {
    return array.reduce((groups, item) => {
        const key = keyFn(item);
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(item);
        return groups;
    }, {});
};
// Object utilities
export const deepClone = (obj) => {
    return JSON.parse(JSON.stringify(obj));
};
export const omit = (obj, keys) => {
    const result = { ...obj };
    keys.forEach(key => delete result[key]);
    return result;
};
export const pick = (obj, keys) => {
    const result = {};
    keys.forEach(key => {
        if (key in obj) {
            result[key] = obj[key];
        }
    });
    return result;
};
// Error utilities
export const getErrorMessage = (error) => {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'Unknown error occurred';
};
export const isNetworkError = (error) => {
    if (error instanceof Error) {
        return error.message.includes('ECONNREFUSED') ||
            error.message.includes('ENOTFOUND') ||
            error.message.includes('ETIMEDOUT') ||
            error.message.includes('network');
    }
    return false;
};
// Async utilities
export const sleep = (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
};
export const timeout = (promise, ms) => {
    return Promise.race([
        promise,
        new Promise((_, reject) => setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms))
    ]);
};
export const retry = async (fn, options = {}) => {
    const { attempts = 3, delay = 1000, backoff = 2, shouldRetry = () => true } = options;
    let lastError;
    let currentDelay = delay;
    for (let attempt = 1; attempt <= attempts; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === attempts || !shouldRetry(error)) {
                throw error;
            }
            await sleep(currentDelay);
            currentDelay *= backoff;
        }
    }
    throw lastError;
};
// Environment utilities
export const getEnvVar = (name, defaultValue) => {
    return process.env[name] || defaultValue;
};
export const isProduction = () => {
    return process.env.NODE_ENV === 'production';
};
export const isDevelopment = () => {
    return process.env.NODE_ENV === 'development';
};
export const isTest = () => {
    return process.env.NODE_ENV === 'test' || process.env.ARIEN_TEST_MODE === 'true';
};
// Terminal utilities
export const getTerminalSize = () => {
    return {
        width: process.stdout.columns || 80,
        height: process.stdout.rows || 24
    };
};
export const clearTerminal = () => {
    process.stdout.write('\x1b[2J\x1b[0f');
};
export const moveCursor = (x, y) => {
    process.stdout.write(`\x1b[${y};${x}H`);
};
// Color utilities (for non-chalk usage)
export const stripAnsi = (str) => {
    return str.replace(/\x1b\[[0-9;]*m/g, '');
};
export const getStringWidth = (str) => {
    return stripAnsi(str).length;
};
// Logging utilities
export const createLogger = (prefix) => {
    return {
        info: (message, ...args) => {
            if (!isTest()) {
                console.log(`[${prefix}] ${message}`, ...args);
            }
        },
        warn: (message, ...args) => {
            if (!isTest()) {
                console.warn(`[${prefix}] ${message}`, ...args);
            }
        },
        error: (message, ...args) => {
            if (!isTest()) {
                console.error(`[${prefix}] ${message}`, ...args);
            }
        },
        debug: (message, ...args) => {
            if (isDevelopment() && !isTest()) {
                console.debug(`[${prefix}] ${message}`, ...args);
            }
        }
    };
};
// Export all utilities
export default {
    // Platform
    isWindows,
    isMacOS,
    isLinux,
    isWSL,
    // String
    truncateString,
    capitalizeFirst,
    camelToKebab,
    kebabToCamel,
    // Time
    formatDuration,
    formatTimestamp,
    formatRelativeTime,
    // File/Path
    resolvePath,
    getHomeDirectory,
    getConfigDirectory,
    getDataDirectory,
    // Validation
    isValidUrl,
    isValidEmail,
    isValidApiKey,
    // Array
    chunk,
    unique,
    groupBy,
    // Object
    deepClone,
    omit,
    pick,
    // Error
    getErrorMessage,
    isNetworkError,
    // Async
    sleep,
    timeout,
    retry,
    // Environment
    getEnvVar,
    isProduction,
    isDevelopment,
    isTest,
    // Terminal
    getTerminalSize,
    clearTerminal,
    moveCursor,
    stripAnsi,
    getStringWidth,
    // Logging
    createLogger
};
//# sourceMappingURL=index.js.map