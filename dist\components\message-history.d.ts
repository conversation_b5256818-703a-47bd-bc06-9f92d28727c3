import { EventEmitter } from 'events';
import { LLMMessage } from '@/types';
export interface MessageHistoryOptions {
    maxMessages?: number;
    groupByDate?: boolean;
    showMetadata?: boolean;
    autoSave?: boolean;
    searchEnabled?: boolean;
}
export interface MessageGroup {
    date: Date;
    label: string;
    messages: LLMMessage[];
    count: number;
}
export interface SearchResult {
    message: LLMMessage;
    sessionId: string;
    sessionName: string;
    matchedText: string;
    context: LLMMessage[];
}
export declare class MessageHistoryManager extends EventEmitter {
    private options;
    private messageIndex;
    private sessionMessages;
    private searchIndex;
    constructor(options?: MessageHistoryOptions);
    /**
     * Add message to history
     */
    addMessage(message: LLMMessage, sessionId: string): void;
    /**
     * Get messages for a session
     */
    getSessionMessages(sessionId: string): LLMMessage[];
    /**
     * Get all messages across all sessions
     */
    getAllMessages(): LLMMessage[];
    /**
     * Get messages grouped by date
     */
    getMessagesByDate(sessionId?: string): MessageGroup[];
    /**
     * Format date label for grouping
     */
    private formatDateLabel;
    /**
     * Search messages
     */
    searchMessages(query: string, options?: {
        sessionId?: string;
        role?: string;
        dateRange?: {
            start: Date;
            end: Date;
        };
        caseSensitive?: boolean;
        exactMatch?: boolean;
    }): SearchResult[];
    /**
     * Find which session a message belongs to
     */
    private findMessageSession;
    /**
     * Get context messages around a specific message
     */
    private getMessageContext;
    /**
     * Extract matched text with surrounding context
     */
    private extractMatchedText;
    /**
     * Update search index for a message
     */
    private updateSearchIndex;
    /**
     * Get message statistics
     */
    getStatistics(): {
        totalMessages: number;
        messagesByRole: Record<string, number>;
        messagesBySession: Record<string, number>;
        dateRange: {
            start: Date;
            end: Date;
        } | null;
        averageMessageLength: number;
        mostActiveDay: string | null;
    };
    /**
     * Export message history
     */
    exportHistory(exportFormat?: 'json' | 'text'): string;
    /**
     * Import message history
     */
    importHistory(data: string): void;
    /**
     * Clear message history
     */
    clearHistory(sessionId?: string): void;
    /**
     * Enforce message limit
     */
    private enforceMessageLimit;
    /**
     * Get recent messages
     */
    getRecentMessages(count?: number, sessionId?: string): LLMMessage[];
    /**
     * Format message for display
     */
    formatMessageForDisplay(message: LLMMessage, showMetadata?: boolean): string;
    /**
     * Format role for display
     */
    private formatRole;
}
//# sourceMappingURL=message-history.d.ts.map