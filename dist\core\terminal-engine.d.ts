import { EventEmitter } from 'events';
import { TerminalState, TerminalSession } from '@/types';
export declare class TerminalEngine extends EventEmitter {
    private state;
    private configManager;
    private sessionManager;
    private llmManager;
    private toolManager;
    private slashCommandManager;
    private isProcessing;
    constructor();
    private initializeState;
    processUserInput(input: string): Promise<void>;
    private processChatMessage;
    private processToolCalls;
    private generateFollowUpResponse;
    private getConversationContext;
    private getSystemPrompt;
    private updateMessageHistory;
    private emitMessage;
    private emitStateChange;
    getState(): TerminalState;
    getCurrentSession(): TerminalSession | null;
    createNewSession(name?: string): TerminalSession;
    switchSession(sessionId: string): TerminalSession;
    deleteSession(sessionId: string): boolean;
    clearCurrentSession(): void;
    switchProvider(provider: 'deepseek' | 'ollama', model?: string): Promise<void>;
    switchModel(model: string): Promise<void>;
    setWorkingDirectory(directory: string): void;
    getAvailableCommands(): string[];
    isReady(): boolean;
    initialize(): Promise<void>;
    destroy(): void;
}
//# sourceMappingURL=terminal-engine.d.ts.map