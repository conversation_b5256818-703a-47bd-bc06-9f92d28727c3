import type { ToolDefinition } from '@/types';
export interface SystemPromptOptions {
    includeToolUsage?: boolean;
    includeExamples?: boolean;
    includeGuidelines?: boolean;
    customInstructions?: string;
    userContext?: {
        name?: string;
        preferences?: string[];
        workingDirectory?: string;
        operatingSystem?: string;
    };
}
export declare class SystemPrompts {
    /**
     * Generate comprehensive system prompt
     */
    static generateSystemPrompt(tools: ToolDefinition[], options?: SystemPromptOptions): string;
    /**
     * Core identity section
     */
    private static getCoreIdentity;
    /**
     * Core capabilities section
     */
    private static getCoreCapabilities;
    /**
     * Important guidelines section
     */
    private static getImportantGuidelines;
    /**
     * Tool usage instructions
     */
    private static getToolUsageInstructions;
    /**
     * Format tool parameters
     */
    private static formatToolParameters;
    /**
     * Examples section
     */
    private static getExamples;
    /**
     * User context section
     */
    private static getUserContextSection;
    /**
     * Final reminders section
     */
    private static getFinalReminders;
    /**
     * Generate prompt for specific scenarios
     */
    static generateScenarioPrompt(scenario: 'development' | 'sysadmin' | 'data-processing' | 'general'): string;
    /**
     * Generate safety-focused prompt
     */
    static generateSafetyPrompt(): string;
    /**
     * Generate context-aware prompt
     */
    static generateContextAwarePrompt(tools: ToolDefinition[], userHistory: string[], currentDirectory: string, options?: SystemPromptOptions): string;
}
//# sourceMappingURL=system-prompts.d.ts.map