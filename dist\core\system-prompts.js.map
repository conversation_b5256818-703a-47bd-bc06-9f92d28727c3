{"version": 3, "file": "system-prompts.js", "sourceRoot": "", "sources": ["../../src/core/system-prompts.ts"], "names": [], "mappings": "AAeA,MAAM,OAAO,aAAa;IACxB;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAChC,KAAuB,EACvB,UAA+B,EAAE;QAEjC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,iCAAiC;QACjC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QAEtC,oBAAoB;QACpB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAE1C,uBAAuB;QACvB,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YACxC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,WAAW;QACX,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACtC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACpC,CAAC;QAED,eAAe;QACf,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,QAAQ,CAAC,IAAI,CAAC,2BAA2B,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,kBAAkB;QAClB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAExC,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe;QAC5B,OAAO;;;;;;;;;iDASsC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB;QAChC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA4BS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB;QACnC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA4BoC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,KAAuB;QAC7D,IAAI,OAAO,GAAG;;;;CAIjB,CAAC;QAEE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI;mBACd,IAAI,CAAC,WAAW;;mBAEhB,IAAI,CAAC,KAAK,CAAC,IAAI;uBACX,IAAI,CAAC,KAAK,CAAC,OAAO;0BACf,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;4BACjD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB;;;EAGnF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAG7D,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC;;;;CAI3C,CAAC;QACE,CAAC;QAED,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;sDAoBuC,CAAC;QAEnD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,UAAe;QACjD,IAAI,CAAC,UAAU,CAAC,UAAU;YAAE,OAAO,eAAe,CAAC;QAEnD,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAgB,EAAE,EAAE;YACtF,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;YACrF,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,gBAAgB,CAAC;YACzD,OAAO,OAAO,IAAI,OAAO,IAAI,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,WAAW;QACxB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCJ,CAAC;IACN,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,OAAwD;QAC3F,IAAI,OAAO,GAAG,mBAAmB,CAAC;QAElC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,OAAO,IAAI,aAAa,OAAO,CAAC,IAAI,IAAI,CAAC;QAC3C,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,IAAI,0BAA0B,OAAO,CAAC,gBAAgB,IAAI,CAAC;QACpE,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,IAAI,yBAAyB,OAAO,CAAC,eAAe,IAAI,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,OAAO,IAAI,qBAAqB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9F,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB;QAC9B,OAAO;;;;;;;;;;;2GAWgG,CAAC;IAC1G,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,sBAAsB,CAAC,QAAoE;QACvG,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAEjD,MAAM,eAAe,GAAG;YACtB,WAAW,EAAE;;;;;;;;;iCASc;YAE3B,QAAQ,EAAE;;;;;;;;;2BASW;YAErB,iBAAiB,EAAE;;;;;;;;;qBASJ;YAEf,OAAO,EAAE;;;;;;;;2BAQY;SACtB,CAAC;QAEF,OAAO,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB;QAChC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+EA6BoE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,0BAA0B,CACtC,KAAuB,EACvB,WAAqB,EACrB,gBAAwB,EACxB,UAA+B,EAAE;QAEjC,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,cAAc,GAAG,wBAAwB,CAAC;QAC9C,cAAc,IAAI,0BAA0B,gBAAgB,IAAI,CAAC;QAEjE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,cAAc,IAAI,wBAAwB,CAAC;YAC3C,cAAc,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,UAAU,GAAG,cAAc,CAAC;IACrC,CAAC;CACF"}