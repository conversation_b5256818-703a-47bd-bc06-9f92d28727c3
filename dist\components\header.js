import { EventEmitter } from 'events';
import chalk from 'chalk';
import { format } from 'date-fns';
import { platform, hostname, userInfo } from 'os';
export class TerminalHeader extends EventEmitter {
    options;
    systemInfo;
    lastUpdate = new Date();
    constructor(options = {}) {
        super();
        this.options = {
            showLogo: true,
            showSystemInfo: false,
            showSessionInfo: true,
            showProviderInfo: true,
            showWorkingDirectory: true,
            showTimestamp: false,
            compact: false,
            customTitle: '',
            ...options
        };
        this.systemInfo = this.collectSystemInfo();
    }
    /**
     * Collect system information
     */
    collectSystemInfo() {
        try {
            const user = userInfo();
            return {
                platform: platform(),
                hostname: hostname(),
                username: user.username,
                nodeVersion: process.version,
                workingDirectory: process.cwd()
            };
        }
        catch (error) {
            return {
                platform: 'unknown',
                hostname: 'unknown',
                username: 'unknown',
                nodeVersion: process.version,
                workingDirectory: process.cwd()
            };
        }
    }
    /**
     * Generate header content based on terminal state
     */
    generateHeader(state) {
        this.lastUpdate = new Date();
        if (this.options.compact) {
            return this.generateCompactHeader(state);
        }
        else {
            return this.generateFullHeader(state);
        }
    }
    /**
     * Generate compact header (single line)
     */
    generateCompactHeader(state) {
        const parts = [];
        // Logo/Title
        if (this.options.showLogo) {
            const title = this.options.customTitle || 'Arien AI';
            parts.push(chalk.cyan.bold(title));
        }
        // Session info
        if (this.options.showSessionInfo && state.currentSession) {
            const sessionName = this.truncateText(state.currentSession.name, 15);
            parts.push(chalk.blue(`Session: ${sessionName}`));
        }
        // Provider info
        if (this.options.showProviderInfo) {
            const provider = state.config.provider || 'None';
            const model = state.config.model || 'None';
            parts.push(chalk.green(`${provider}/${this.truncateText(model, 12)}`));
        }
        // Working directory
        if (this.options.showWorkingDirectory) {
            const workingDir = state.currentSession?.workingDirectory || this.systemInfo.workingDirectory;
            parts.push(chalk.yellow(this.truncateText(workingDir, 20)));
        }
        // Status indicator
        const statusIndicator = this.getStatusIndicator(state);
        parts.push(statusIndicator);
        return parts.join(chalk.gray(' │ '));
    }
    /**
     * Generate full header (multi-line)
     */
    generateFullHeader(state) {
        const lines = [];
        // Title line
        const titleLine = this.generateTitleLine();
        lines.push(titleLine);
        // Info line
        const infoLine = this.generateInfoLine(state);
        lines.push(infoLine);
        // System info line (if enabled)
        if (this.options.showSystemInfo) {
            const systemLine = this.generateSystemLine();
            lines.push(systemLine);
        }
        return lines.join('\n');
    }
    /**
     * Generate title line
     */
    generateTitleLine() {
        const title = this.options.customTitle || 'Arien AI CLI';
        const subtitle = 'Modern CLI with LLM Integration & Function Calling';
        if (this.options.showTimestamp) {
            const timestamp = chalk.gray(format(this.lastUpdate, 'HH:mm:ss'));
            return `${chalk.cyan.bold(title)} ${chalk.gray('•')} ${chalk.white(subtitle)} ${timestamp}`;
        }
        return `${chalk.cyan.bold(title)} ${chalk.gray('•')} ${chalk.white(subtitle)}`;
    }
    /**
     * Generate info line with session and provider details
     */
    generateInfoLine(state) {
        const parts = [];
        // Session information
        if (this.options.showSessionInfo) {
            const sessionInfo = this.formatSessionInfo(state.currentSession);
            parts.push(sessionInfo);
        }
        // Provider information
        if (this.options.showProviderInfo) {
            const providerInfo = this.formatProviderInfo(state.config);
            parts.push(providerInfo);
        }
        // Working directory
        if (this.options.showWorkingDirectory) {
            const workingDir = state.currentSession?.workingDirectory || this.systemInfo.workingDirectory;
            const dirInfo = `${chalk.yellow('📁')} ${chalk.white(this.truncateText(workingDir, 40))}`;
            parts.push(dirInfo);
        }
        // Status
        const statusInfo = this.getDetailedStatus(state);
        parts.push(statusInfo);
        return parts.join(chalk.gray(' │ '));
    }
    /**
     * Generate system information line
     */
    generateSystemLine() {
        const parts = [
            `${chalk.blue('💻')} ${chalk.white(this.systemInfo.hostname)}`,
            `${chalk.green('👤')} ${chalk.white(this.systemInfo.username)}`,
            `${chalk.magenta('🟢')} ${chalk.white(this.systemInfo.nodeVersion)}`,
            `${chalk.cyan('🖥️')} ${chalk.white(this.systemInfo.platform)}`
        ];
        return parts.join(chalk.gray(' │ '));
    }
    /**
     * Format session information
     */
    formatSessionInfo(session) {
        if (!session) {
            return `${chalk.red('📝')} ${chalk.gray('No Session')}`;
        }
        const sessionName = this.truncateText(session.name, 20);
        const messageCount = session.messages.length;
        const duration = this.getSessionDuration(session);
        return `${chalk.blue('📝')} ${chalk.white(sessionName)} ${chalk.gray(`(${messageCount} msgs, ${duration})`)}`;
    }
    /**
     * Format provider information
     */
    formatProviderInfo(config) {
        const provider = config.provider || 'None';
        const model = config.model || 'None';
        const providerIcon = this.getProviderIcon(provider);
        const connectionStatus = this.getConnectionStatus(provider);
        return `${providerIcon} ${chalk.white(provider)}/${chalk.cyan(this.truncateText(model, 15))} ${connectionStatus}`;
    }
    /**
     * Get provider icon
     */
    getProviderIcon(provider) {
        const icons = {
            deepseek: '🧠',
            ollama: '🦙',
            openai: '🤖',
            anthropic: '🎭'
        };
        return icons[provider] || '🔮';
    }
    /**
     * Get connection status indicator
     */
    getConnectionStatus(provider) {
        // This would be connected to actual connection status
        // For now, return a placeholder
        return chalk.green('●');
    }
    /**
     * Get status indicator for compact mode
     */
    getStatusIndicator(state) {
        if (state.isProcessing) {
            return chalk.yellow('⚡ Processing');
        }
        if (!state.config.provider) {
            return chalk.red('❌ Not Configured');
        }
        return chalk.green('✅ Ready');
    }
    /**
     * Get detailed status information
     */
    getDetailedStatus(state) {
        if (state.isProcessing) {
            const currentCommand = state.currentCommand ?
                this.truncateText(state.currentCommand, 30) :
                'Unknown';
            return `${chalk.yellow('⚡')} ${chalk.white('Processing:')} ${chalk.gray(currentCommand)}`;
        }
        if (!state.config.provider) {
            return `${chalk.red('❌')} ${chalk.white('Not Configured')}`;
        }
        const messageCount = state.messageHistory.length;
        return `${chalk.green('✅')} ${chalk.white('Ready')} ${chalk.gray(`(${messageCount} messages)`)}`;
    }
    /**
     * Get session duration
     */
    getSessionDuration(session) {
        const now = new Date();
        const duration = now.getTime() - session.createdAt.getTime();
        const minutes = Math.floor(duration / 60000);
        const hours = Math.floor(minutes / 60);
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        }
        else if (minutes > 0) {
            return `${minutes}m`;
        }
        else {
            return '<1m';
        }
    }
    /**
     * Truncate text to specified length
     */
    truncateText(text, maxLength) {
        if (text.length <= maxLength) {
            return text;
        }
        return text.slice(0, maxLength - 3) + '...';
    }
    /**
     * Generate ASCII art logo
     */
    generateLogo() {
        return chalk.cyan(`
    ╔═══════════════════════════════════════════════════════════════╗
    ║                        🚀 Arien AI CLI 🚀                    ║
    ║          Modern CLI with LLM Integration & Function Calling   ║
    ╚═══════════════════════════════════════════════════════════════╝
    `);
    }
    /**
     * Generate welcome message
     */
    generateWelcomeMessage(state) {
        const lines = [];
        lines.push(chalk.cyan.bold('Welcome to Arien AI CLI! 🎉'));
        lines.push('');
        if (state.config.provider) {
            lines.push(chalk.green(`✅ Connected to ${state.config.provider} (${state.config.model})`));
        }
        else {
            lines.push(chalk.yellow('⚠️  No provider configured. Run /provider to set up.'));
        }
        if (state.currentSession) {
            lines.push(chalk.blue(`📝 Session: ${state.currentSession.name}`));
        }
        else {
            lines.push(chalk.gray('📝 No active session'));
        }
        lines.push('');
        lines.push(chalk.white('Available commands:'));
        lines.push(chalk.gray('  /help     - Show help'));
        lines.push(chalk.gray('  /provider - Manage providers'));
        lines.push(chalk.gray('  /session  - Manage sessions'));
        lines.push(chalk.gray('  /model    - Switch models'));
        lines.push(chalk.gray('  /history  - View message history'));
        lines.push('');
        lines.push(chalk.cyan('Type your message or command to get started!'));
        return lines.join('\n');
    }
    /**
     * Update header options
     */
    updateOptions(options) {
        this.options = { ...this.options, ...options };
        this.emit('options-updated', this.options);
    }
    /**
     * Get current options
     */
    getOptions() {
        return { ...this.options };
    }
    /**
     * Refresh system information
     */
    refreshSystemInfo() {
        this.systemInfo = this.collectSystemInfo();
        this.emit('system-info-updated', this.systemInfo);
    }
    /**
     * Get system information
     */
    getSystemInfo() {
        return { ...this.systemInfo };
    }
    /**
     * Generate status bar
     */
    generateStatusBar(state) {
        const parts = [];
        // Connection status
        const connectionStatus = state.config.provider ?
            chalk.green('🟢 Connected') :
            chalk.red('🔴 Disconnected');
        parts.push(connectionStatus);
        // Message count
        const messageCount = state.messageHistory.length;
        parts.push(chalk.blue(`💬 ${messageCount} messages`));
        // Working directory
        const workingDir = state.currentSession?.workingDirectory || this.systemInfo.workingDirectory;
        parts.push(chalk.yellow(`📁 ${this.truncateText(workingDir, 25)}`));
        // Timestamp
        if (this.options.showTimestamp) {
            const timestamp = format(new Date(), 'HH:mm:ss');
            parts.push(chalk.gray(`🕐 ${timestamp}`));
        }
        return parts.join(chalk.gray(' │ '));
    }
}
//# sourceMappingURL=header.js.map