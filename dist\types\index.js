import { z } from 'zod';
// Configuration Schemas
export const ConfigSchema = z.object({
    provider: z.enum(['deepseek', 'ollama']),
    model: z.string(),
    apiKey: z.string().optional(),
    baseUrl: z.string().optional(),
    maxTokens: z.number().default(4096),
    temperature: z.number().min(0).max(2).default(0.7),
    sessionId: z.string().optional(),
    workingDirectory: z.string().optional(),
    autoApprove: z.boolean().default(false),
    retryAttempts: z.number().default(3),
    timeout: z.number().default(30000),
});
// Error Types
export class ArienError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'ArienError';
    }
}
export class ProviderError extends ArienError {
    constructor(message, details) {
        super(message, 'PROVIDER_ERROR', details);
    }
}
export class CommandError extends ArienError {
    constructor(message, details) {
        super(message, 'COMMAND_ERROR', details);
    }
}
export class ConfigError extends ArienError {
    constructor(message, details) {
        super(message, 'CONFIG_ERROR', details);
    }
}
//# sourceMappingURL=index.js.map