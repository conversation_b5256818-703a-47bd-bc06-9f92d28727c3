{"version": 3, "file": "llm-manager.js", "sourceRoot": "", "sources": ["../../src/core/llm-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAkB,MAAM,sBAAsB,CAAC;AACxE,OAAO,EAAE,cAAc,EAAgB,MAAM,oBAAoB,CAAC;AAClE,OAAO,EAA8B,aAAa,EAAU,MAAM,SAAS,CAAC;AAC5E,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAIzC,MAAM,OAAO,UAAU;IACb,QAAQ,GAA+B,IAAI,CAAC;IAC5C,aAAa,CAAgB;IAErC;QACE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;IACnD,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAE3C,IAAI,MAAM,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,IAAI,aAAa,CAAC,8BAA8B,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,cAAc,GAAmB;gBACrC,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;YAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,aAAa,CAAC,6BAA6B,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,YAAY,GAAiB;gBACjC,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;YAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,CAAC,YAAY,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,aAAa,CAAC,yBAAyB,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACpD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,aAAa,CAAC,wBAAwB,MAAM,CAAC,QAAQ,WAAW,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,QAAsB,EACtB,KAAwB,EACxB,OAIC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;IAClD,CAAC;IAEM,kBAAkB;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,QAAQ,CAAC;IACzB,CAAC;IAEM,eAAe;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAA+B,EAAE,KAAc;QACzE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,qCAAqC;YACrC,MAAM,YAAY,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC1E,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAChD,CAAC;QAED,2CAA2C;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,KAAa;QACpC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAElD,8BAA8B;QAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,aAAa,CAAC,UAAU,KAAK,oCAAoC,eAAe,GAAG,CAAC,CAAC;QACjG,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEvC,gCAAgC;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,eAAe,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,YAAY,gBAAgB,EAAE,CAAC;gBAChF,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YACxC,CAAC;iBAAM,IAAI,eAAe,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,YAAY,cAAc,EAAE,CAAC;gBACnF,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAEM,oBAAoB,CAAC,MAA8C;QACxE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAElD,IAAI,eAAe,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,YAAY,gBAAgB,EAAE,CAAC;YAChF,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAiC,CAAC,CAAC;YAE9D,wBAAwB;YACxB,IAAI,QAAQ,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAClD,CAAC;YACD,IAAI,SAAS,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;aAAM,IAAI,eAAe,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,YAAY,cAAc,EAAE,CAAC;YACnF,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAA+B,CAAC,CAAC;YAE5D,wBAAwB;YACxB,IAAI,SAAS,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,aAAa,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAChE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,SAAS,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEM,iBAAiB;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;IACnC,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAED,0BAA0B;IACnB,KAAK,CAAC,SAAS,CAAC,SAAiB;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,YAAY,cAAc,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,aAAa,CAAC,gDAAgD,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,YAAY,cAAc,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,aAAa,CAAC,iDAAiD,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,SAAiB;QACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,YAAY,cAAc,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,aAAa,CAAC,oDAAoD,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;CACF"}