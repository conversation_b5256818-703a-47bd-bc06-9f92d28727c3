import { EventEmitter } from 'events';
import { TerminalState } from '@/types';
export interface HeaderOptions {
    showLogo?: boolean;
    showSystemInfo?: boolean;
    showSessionInfo?: boolean;
    showProviderInfo?: boolean;
    showWorkingDirectory?: boolean;
    showTimestamp?: boolean;
    compact?: boolean;
    customTitle?: string;
}
export interface SystemInfo {
    platform: string;
    hostname: string;
    username: string;
    nodeVersion: string;
    workingDirectory: string;
}
export declare class TerminalHeader extends EventEmitter {
    private options;
    private systemInfo;
    private lastUpdate;
    constructor(options?: HeaderOptions);
    /**
     * Collect system information
     */
    private collectSystemInfo;
    /**
     * Generate header content based on terminal state
     */
    generateHeader(state: TerminalState): string;
    /**
     * Generate compact header (single line)
     */
    private generateCompactHeader;
    /**
     * Generate full header (multi-line)
     */
    private generateFullHeader;
    /**
     * Generate title line
     */
    private generateTitleLine;
    /**
     * Generate info line with session and provider details
     */
    private generateInfoLine;
    /**
     * Generate system information line
     */
    private generateSystemLine;
    /**
     * Format session information
     */
    private formatSessionInfo;
    /**
     * Format provider information
     */
    private formatProviderInfo;
    /**
     * Get provider icon
     */
    private getProviderIcon;
    /**
     * Get connection status indicator
     */
    private getConnectionStatus;
    /**
     * Get status indicator for compact mode
     */
    private getStatusIndicator;
    /**
     * Get detailed status information
     */
    private getDetailedStatus;
    /**
     * Get session duration
     */
    private getSessionDuration;
    /**
     * Truncate text to specified length
     */
    private truncateText;
    /**
     * Generate ASCII art logo
     */
    generateLogo(): string;
    /**
     * Generate welcome message
     */
    generateWelcomeMessage(state: TerminalState): string;
    /**
     * Update header options
     */
    updateOptions(options: Partial<HeaderOptions>): void;
    /**
     * Get current options
     */
    getOptions(): Required<HeaderOptions>;
    /**
     * Refresh system information
     */
    refreshSystemInfo(): void;
    /**
     * Get system information
     */
    getSystemInfo(): SystemInfo;
    /**
     * Generate status bar
     */
    generateStatusBar(state: TerminalState): string;
}
//# sourceMappingURL=header.d.ts.map