import { Options } from 'ora';
export interface SpinnerOptions extends Partial<Options> {
}
export declare class ModernSpinner {
    private spinner;
    private static activeSpinners;
    constructor(options?: SpinnerOptions);
    start(text?: string): ModernSpinner;
    stop(): ModernSpinner;
    succeed(text?: string): ModernSpinner;
    fail(text?: string): ModernSpinner;
    warn(text?: string): ModernSpinner;
    info(text?: string): ModernSpinner;
    setText(text: string): ModernSpinner;
    setColor(color: SpinnerOptions['color']): ModernSpinner;
    setSpinner(spinner: any): ModernSpinner;
    get isSpinning(): boolean;
    get text(): string;
    static stopAll(): void;
    static createProcessingSpinner(text?: string): ModernSpinner;
    static createLoadingSpinner(text?: string): ModernSpinner;
    static createThinkingSpinner(text?: string): ModernSpinner;
    static createExecutingSpinner(text?: string): ModernSpinner;
    static createConnectingSpinner(text?: string): ModernSpinner;
    static createToolExecutionSpinner(text?: string): ModernSpinner;
    static createRetrySpinner(text?: string): ModernSpinner;
    static withSpinner<T>(promise: Promise<T>, options?: {
        text?: string;
        successText?: string;
        failText?: string;
        spinner?: ModernSpinner;
    }): Promise<T>;
    static withProgressSpinner<T>(promise: Promise<T>, progressCallback: (updateText: (text: string) => void) => void, options?: {
        initialText?: string;
        successText?: string;
        failText?: string;
    }): Promise<T>;
    static readonly CUSTOM_SPINNERS: {
        modernDots: {
            interval: number;
            frames: string[];
        };
        modernBars: {
            interval: number;
            frames: string[];
        };
        modernCircle: {
            interval: number;
            frames: string[];
        };
        modernArrow: {
            interval: number;
            frames: string[];
        };
        modernPulse: {
            interval: number;
            frames: string[];
        };
    };
    static createCustomSpinner(spinnerName: keyof typeof ModernSpinner.CUSTOM_SPINNERS, text?: string, color?: SpinnerOptions['color']): ModernSpinner;
}
//# sourceMappingURL=spinner.d.ts.map