import { TerminalLayout } from './terminal-layout';
import { TerminalEngine } from '@/core/terminal-engine';
export declare class Terminal {
    private layout;
    private engine;
    private configManager;
    private isInitialized;
    constructor();
    private setupEventHandlers;
    private handleMessage;
    private handleUserInput;
    start(): Promise<void>;
    private showWelcome;
    private runOnboarding;
    private initializeEngine;
    private showReadyMessage;
    quickStart(): Promise<void>;
    restart(): Promise<void>;
    reconfigure(): Promise<void>;
    getEngine(): TerminalEngine;
    getLayout(): TerminalLayout;
    isReady(): boolean;
    destroy(): void;
    executeCommand(command: string): Promise<void>;
    addSystemMessage(message: string): void;
    showStatus(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void;
}
//# sourceMappingURL=terminal.d.ts.map