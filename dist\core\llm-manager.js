import { DeepseekProvider } from '@/providers/deepseek';
import { OllamaProvider } from '@/providers/ollama';
import { ProviderError } from '@/types';
import { ConfigManager } from './config';
export class LLMManager {
    provider = null;
    configManager;
    constructor() {
        this.configManager = ConfigManager.getInstance();
    }
    async initializeProvider() {
        const config = this.configManager.getAll();
        if (config.provider === 'deepseek') {
            if (!config.apiKey) {
                throw new ProviderError('Deepseek API key is required');
            }
            const deepseekConfig = {
                apiKey: config.apiKey,
                model: config.model,
                baseUrl: config.baseUrl,
                maxTokens: config.maxTokens,
                temperature: config.temperature,
                timeout: config.timeout
            };
            this.provider = new DeepseekProvider(deepseekConfig);
        }
        else if (config.provider === 'ollama') {
            if (!config.baseUrl) {
                throw new ProviderError('Ollama base URL is required');
            }
            const ollamaConfig = {
                baseUrl: config.baseUrl,
                model: config.model,
                maxTokens: config.maxTokens,
                temperature: config.temperature,
                timeout: config.timeout
            };
            this.provider = new OllamaProvider(ollamaConfig);
        }
        else {
            throw new ProviderError(`Unsupported provider: ${config.provider}`);
        }
        // Validate connection
        const isConnected = await this.validateConnection();
        if (!isConnected) {
            throw new ProviderError(`Failed to connect to ${config.provider} provider`);
        }
    }
    async generateResponse(messages, tools, options) {
        if (!this.provider) {
            await this.initializeProvider();
        }
        if (!this.provider) {
            throw new ProviderError('No provider initialized');
        }
        return await this.provider.generateResponse(messages, tools, options);
    }
    async validateConnection() {
        if (!this.provider) {
            return false;
        }
        try {
            return await this.provider.validateConnection();
        }
        catch (error) {
            console.warn('Provider connection validation failed:', error);
            return false;
        }
    }
    async getAvailableModels() {
        if (!this.provider) {
            await this.initializeProvider();
        }
        if (!this.provider) {
            throw new ProviderError('No provider initialized');
        }
        return await this.provider.getAvailableModels();
    }
    getCurrentProvider() {
        const config = this.configManager.getAll();
        return config.provider;
    }
    getCurrentModel() {
        const config = this.configManager.getAll();
        return config.model;
    }
    async switchProvider(provider, model) {
        this.configManager.set('provider', provider);
        if (model) {
            this.configManager.set('model', model);
        }
        else {
            // Set default model for the provider
            const defaultModel = provider === 'deepseek' ? 'deepseek-chat' : 'llama2';
            this.configManager.set('model', defaultModel);
        }
        // Reset provider to force reinitialization
        this.provider = null;
        await this.initializeProvider();
    }
    async switchModel(model) {
        const currentProvider = this.getCurrentProvider();
        // Validate model is available
        const availableModels = await this.getAvailableModels();
        if (!availableModels.includes(model)) {
            throw new ProviderError(`Model '${model}' is not available for provider '${currentProvider}'`);
        }
        this.configManager.set('model', model);
        // Update provider configuration
        if (this.provider) {
            if (currentProvider === 'deepseek' && this.provider instanceof DeepseekProvider) {
                this.provider.updateConfig({ model });
            }
            else if (currentProvider === 'ollama' && this.provider instanceof OllamaProvider) {
                this.provider.updateConfig({ model });
            }
        }
    }
    updateProviderConfig(config) {
        if (!this.provider) {
            throw new ProviderError('No provider initialized');
        }
        const currentProvider = this.getCurrentProvider();
        if (currentProvider === 'deepseek' && this.provider instanceof DeepseekProvider) {
            this.provider.updateConfig(config);
            // Update config manager
            if ('apiKey' in config && config.apiKey) {
                this.configManager.set('apiKey', config.apiKey);
            }
            if ('baseUrl' in config && config.baseUrl) {
                this.configManager.set('baseUrl', config.baseUrl);
            }
        }
        else if (currentProvider === 'ollama' && this.provider instanceof OllamaProvider) {
            this.provider.updateConfig(config);
            // Update config manager
            if ('baseUrl' in config && config.baseUrl) {
                this.configManager.set('baseUrl', config.baseUrl);
            }
        }
        // Update common config
        if ('maxTokens' in config && config.maxTokens) {
            this.configManager.set('maxTokens', config.maxTokens);
        }
        if ('temperature' in config && config.temperature !== undefined) {
            this.configManager.set('temperature', config.temperature);
        }
        if ('timeout' in config && config.timeout) {
            this.configManager.set('timeout', config.timeout);
        }
    }
    getProviderConfig() {
        if (!this.provider) {
            return null;
        }
        return this.provider.getConfig();
    }
    isInitialized() {
        return this.provider !== null;
    }
    async reinitialize() {
        this.provider = null;
        await this.initializeProvider();
    }
    // Ollama-specific methods
    async pullModel(modelName) {
        if (!this.provider || !(this.provider instanceof OllamaProvider)) {
            throw new ProviderError('Ollama provider is required for pulling models');
        }
        await this.provider.pullModel(modelName);
    }
    async deleteModel(modelName) {
        if (!this.provider || !(this.provider instanceof OllamaProvider)) {
            throw new ProviderError('Ollama provider is required for deleting models');
        }
        await this.provider.deleteModel(modelName);
    }
    async getModelInfo(modelName) {
        if (!this.provider || !(this.provider instanceof OllamaProvider)) {
            throw new ProviderError('Ollama provider is required for getting model info');
        }
        return await this.provider.getModelInfo(modelName);
    }
}
//# sourceMappingURL=llm-manager.js.map