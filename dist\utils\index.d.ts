/**
 * Utility functions for the Arien AI CLI
 */
export declare const isWindows: () => boolean;
export declare const isMacOS: () => boolean;
export declare const isLinux: () => boolean;
export declare const isWSL: () => boolean;
export declare const truncateString: (str: string, maxLength: number, suffix?: string) => string;
export declare const capitalizeFirst: (str: string) => string;
export declare const camelToKebab: (str: string) => string;
export declare const kebabToCamel: (str: string) => string;
export declare const formatDuration: (ms: number) => string;
export declare const formatTimestamp: (date: Date) => string;
export declare const formatRelativeTime: (date: Date) => string;
export declare const resolvePath: (path: string, basePath?: string) => string;
export declare const getHomeDirectory: () => string;
export declare const getConfigDirectory: () => string;
export declare const getDataDirectory: () => string;
export declare const isValidUrl: (url: string) => boolean;
export declare const isValidEmail: (email: string) => boolean;
export declare const isValidApiKey: (apiKey: string, provider: string) => boolean;
export declare const chunk: <T>(array: T[], size: number) => T[][];
export declare const unique: <T>(array: T[]) => T[];
export declare const groupBy: <T, K extends string | number | symbol>(array: T[], keyFn: (item: T) => K) => Record<K, T[]>;
export declare const deepClone: <T>(obj: T) => T;
export declare const omit: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]) => Omit<T, K>;
export declare const pick: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]) => Pick<T, K>;
export declare const getErrorMessage: (error: unknown) => string;
export declare const isNetworkError: (error: unknown) => boolean;
export declare const sleep: (ms: number) => Promise<void>;
export declare const timeout: <T>(promise: Promise<T>, ms: number) => Promise<T>;
export declare const retry: <T>(fn: () => Promise<T>, options?: {
    attempts?: number;
    delay?: number;
    backoff?: number;
    shouldRetry?: (error: unknown) => boolean;
}) => Promise<T>;
export declare const getEnvVar: (name: string, defaultValue?: string) => string | undefined;
export declare const isProduction: () => boolean;
export declare const isDevelopment: () => boolean;
export declare const isTest: () => boolean;
export declare const getTerminalSize: () => {
    width: number;
    height: number;
};
export declare const clearTerminal: () => void;
export declare const moveCursor: (x: number, y: number) => void;
export declare const stripAnsi: (str: string) => string;
export declare const getStringWidth: (str: string) => number;
export declare const createLogger: (prefix: string) => {
    info: (message: string, ...args: any[]) => void;
    warn: (message: string, ...args: any[]) => void;
    error: (message: string, ...args: any[]) => void;
    debug: (message: string, ...args: any[]) => void;
};
declare const _default: {
    isWindows: () => boolean;
    isMacOS: () => boolean;
    isLinux: () => boolean;
    isWSL: () => boolean;
    truncateString: (str: string, maxLength: number, suffix?: string) => string;
    capitalizeFirst: (str: string) => string;
    camelToKebab: (str: string) => string;
    kebabToCamel: (str: string) => string;
    formatDuration: (ms: number) => string;
    formatTimestamp: (date: Date) => string;
    formatRelativeTime: (date: Date) => string;
    resolvePath: (path: string, basePath?: string) => string;
    getHomeDirectory: () => string;
    getConfigDirectory: () => string;
    getDataDirectory: () => string;
    isValidUrl: (url: string) => boolean;
    isValidEmail: (email: string) => boolean;
    isValidApiKey: (apiKey: string, provider: string) => boolean;
    chunk: <T>(array: T[], size: number) => T[][];
    unique: <T>(array: T[]) => T[];
    groupBy: <T, K extends string | number | symbol>(array: T[], keyFn: (item: T) => K) => Record<K, T[]>;
    deepClone: <T>(obj: T) => T;
    omit: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]) => Omit<T, K>;
    pick: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]) => Pick<T, K>;
    getErrorMessage: (error: unknown) => string;
    isNetworkError: (error: unknown) => boolean;
    sleep: (ms: number) => Promise<void>;
    timeout: <T>(promise: Promise<T>, ms: number) => Promise<T>;
    retry: <T>(fn: () => Promise<T>, options?: {
        attempts?: number;
        delay?: number;
        backoff?: number;
        shouldRetry?: (error: unknown) => boolean;
    }) => Promise<T>;
    getEnvVar: (name: string, defaultValue?: string) => string | undefined;
    isProduction: () => boolean;
    isDevelopment: () => boolean;
    isTest: () => boolean;
    getTerminalSize: () => {
        width: number;
        height: number;
    };
    clearTerminal: () => void;
    moveCursor: (x: number, y: number) => void;
    stripAnsi: (str: string) => string;
    getStringWidth: (str: string) => number;
    createLogger: (prefix: string) => {
        info: (message: string, ...args: any[]) => void;
        warn: (message: string, ...args: any[]) => void;
        error: (message: string, ...args: any[]) => void;
        debug: (message: string, ...args: any[]) => void;
    };
};
export default _default;
//# sourceMappingURL=index.d.ts.map