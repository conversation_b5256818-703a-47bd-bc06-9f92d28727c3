import { ToolDefinition, ShellCommandResult } from '@/types';
export declare class ShellTool {
    private workingDirectory;
    private readonly maxExecutionTime;
    private readonly dangerousCommands;
    constructor(workingDirectory?: string);
    getToolDefinition(): ToolDefinition;
    execute(command: string, options?: {
        workingDirectory?: string;
        timeout?: number;
        requireApproval?: boolean;
    }): Promise<ShellCommandResult>;
    private validateCommand;
    private parseCommand;
    setWorkingDirectory(directory: string): void;
    getWorkingDirectory(): string;
    validateDirectory(directory: string): Promise<boolean>;
    getCurrentDirectory(): Promise<string>;
}
//# sourceMappingURL=shell.d.ts.map