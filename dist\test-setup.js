"use strict";
// Jest setup file for global test configuration
// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
// Mock process.exit to prevent tests from actually exiting
const mockExit = jest.fn();
process.exit = mockExit;
// Reset mocks before each test
beforeEach(() => {
    jest.clearAllMocks();
    mockExit.mockClear();
});
// Global test timeout
jest.setTimeout(30000);
// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.ARIEN_TEST_MODE = 'true';
// Mock blessed screen for terminal tests
jest.mock('blessed', () => ({
    screen: jest.fn(() => ({
        destroy: jest.fn(),
        render: jest.fn(),
        key: jest.fn(),
        on: jest.fn(),
        emit: jest.fn(),
        focused: null,
    })),
    box: jest.fn(() => ({
        setContent: jest.fn(),
        pushLine: jest.fn(),
        setScrollPerc: jest.fn(),
        scroll: jest.fn(),
        show: jest.fn(),
        hide: jest.fn(),
        focus: jest.fn(),
        key: jest.fn(),
        on: jest.fn(),
    })),
    textbox: jest.fn(() => ({
        getValue: jest.fn(() => ''),
        setValue: jest.fn(),
        clearValue: jest.fn(),
        focus: jest.fn(),
        key: jest.fn(),
        on: jest.fn(),
    })),
}));
// Mock ora spinner
jest.mock('ora', () => {
    return jest.fn(() => ({
        start: jest.fn().mockReturnThis(),
        stop: jest.fn().mockReturnThis(),
        succeed: jest.fn().mockReturnThis(),
        fail: jest.fn().mockReturnThis(),
        warn: jest.fn().mockReturnThis(),
        info: jest.fn().mockReturnThis(),
        text: '',
        color: 'cyan',
        isSpinning: false,
    }));
});
// Mock inquirer
jest.mock('inquirer', () => ({
    prompt: jest.fn().mockResolvedValue({}),
}));
// Mock conf for configuration
jest.mock('conf', () => {
    return jest.fn().mockImplementation(() => ({
        get: jest.fn(),
        set: jest.fn(),
        has: jest.fn(),
        delete: jest.fn(),
        clear: jest.fn(),
        store: {},
        path: '/mock/config/path',
    }));
});
// Mock execa for shell commands
jest.mock('execa', () => ({
    execa: jest.fn().mockResolvedValue({
        stdout: '',
        stderr: '',
        exitCode: 0,
    }),
}));
// Mock axios for HTTP requests
jest.mock('axios', () => ({
    create: jest.fn(() => ({
        post: jest.fn().mockResolvedValue({ data: {} }),
        get: jest.fn().mockResolvedValue({ data: {} }),
        defaults: {
            headers: {},
            baseURL: '',
            timeout: 0,
        },
        interceptors: {
            response: {
                use: jest.fn(),
            },
        },
    })),
    post: jest.fn().mockResolvedValue({ data: {} }),
    get: jest.fn().mockResolvedValue({ data: {} }),
}));
// Mock file system operations
jest.mock('fs', () => ({
    existsSync: jest.fn().mockReturnValue(true),
    readFileSync: jest.fn().mockReturnValue('{}'),
    writeFileSync: jest.fn(),
    statSync: jest.fn().mockReturnValue({
        isDirectory: jest.fn().mockReturnValue(true),
    }),
}));
// Mock path operations
jest.mock('path', () => ({
    resolve: jest.fn((...args) => args.join('/')),
    join: jest.fn((...args) => args.join('/')),
}));
// Global error handler for unhandled promises in tests
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
// Cleanup after all tests
afterAll(() => {
    jest.restoreAllMocks();
});
//# sourceMappingURL=test-setup.js.map