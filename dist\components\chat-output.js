import { EventEmitter } from 'events';
import chalk from 'chalk';
import { format } from 'date-fns';
import stripAnsi from 'strip-ansi';
export class ChatOutputProcessor extends EventEmitter {
    options;
    messageBuffer = [];
    maxBufferSize = 1000;
    constructor(options = {}) {
        super();
        this.options = {
            showTimestamps: true,
            showRoleColors: true,
            maxLineLength: 120,
            codeHighlighting: true,
            compactMode: false,
            showTokenCount: false,
            ...options
        };
    }
    /**
     * Format a message for display
     */
    formatMessage(message, metadata) {
        const formatted = {
            id: message.id,
            content: this.processMessageContent(message),
            rawContent: message.content,
            role: message.role,
            timestamp: message.timestamp,
            metadata
        };
        // Add to buffer
        this.addToBuffer(formatted);
        return formatted;
    }
    /**
     * Process message content with formatting
     */
    processMessageContent(message) {
        let content = message.content;
        const role = message.role;
        const timestamp = this.options.showTimestamps ?
            chalk.gray(`[${format(message.timestamp, 'HH:mm:ss')}]`) : '';
        // Role-specific formatting
        const roleFormatted = this.formatRole(role);
        // Process content based on role
        switch (role) {
            case 'user':
                content = this.formatUserMessage(content);
                break;
            case 'assistant':
                content = this.formatAssistantMessage(content);
                break;
            case 'tool':
                content = this.formatToolMessage(content, message.toolCallId);
                break;
            case 'system':
                content = this.formatSystemMessage(content);
                break;
        }
        // Add tool calls if present
        if (message.toolCalls && message.toolCalls.length > 0) {
            content += '\n' + this.formatToolCalls(message.toolCalls);
        }
        // Combine all parts
        const parts = [timestamp, roleFormatted, content].filter(Boolean);
        return parts.join(' ');
    }
    /**
     * Format role indicator
     */
    formatRole(role) {
        if (!this.options.showRoleColors) {
            return `[${role.toUpperCase()}]`;
        }
        const roleColors = {
            user: chalk.cyan.bold('👤 USER'),
            assistant: chalk.green.bold('🤖 AI'),
            tool: chalk.magenta.bold('🔧 TOOL'),
            system: chalk.yellow.bold('⚙️  SYSTEM')
        };
        return roleColors[role] || chalk.white.bold(`[${role.toUpperCase()}]`);
    }
    /**
     * Format user message
     */
    formatUserMessage(content) {
        // Highlight slash commands
        content = content.replace(/^\/(\w+)/g, chalk.cyan.bold('/$1'));
        // Highlight shell commands (basic detection)
        content = content.replace(/^(\w+)\s/g, chalk.green('$1') + ' ');
        return content;
    }
    /**
     * Format assistant message
     */
    formatAssistantMessage(content) {
        // Format code blocks
        if (this.options.codeHighlighting) {
            content = this.formatCodeBlocks(content);
        }
        // Format lists
        content = this.formatLists(content);
        // Format emphasis
        content = this.formatEmphasis(content);
        return content;
    }
    /**
     * Format tool message
     */
    formatToolMessage(content, toolCallId) {
        const toolId = toolCallId ? chalk.gray(`[${toolCallId.slice(0, 8)}]`) : '';
        // Format command output
        const lines = content.split('\n');
        const formattedLines = lines.map(line => {
            // Highlight error lines
            if (line.toLowerCase().includes('error') || line.toLowerCase().includes('failed')) {
                return chalk.red(line);
            }
            // Highlight warning lines
            if (line.toLowerCase().includes('warning') || line.toLowerCase().includes('warn')) {
                return chalk.yellow(line);
            }
            // Highlight success lines
            if (line.toLowerCase().includes('success') || line.toLowerCase().includes('completed')) {
                return chalk.green(line);
            }
            return chalk.gray(line);
        });
        return `${toolId}\n${formattedLines.join('\n')}`;
    }
    /**
     * Format system message
     */
    formatSystemMessage(content) {
        return chalk.yellow(content);
    }
    /**
     * Format tool calls
     */
    formatToolCalls(toolCalls) {
        const formatted = toolCalls.map(call => {
            const functionName = chalk.magenta.bold(call.function.name);
            const args = this.formatToolArguments(call.function.arguments);
            return `🔧 ${functionName}(${args})`;
        });
        return formatted.join('\n');
    }
    /**
     * Format tool arguments
     */
    formatToolArguments(args) {
        try {
            const parsed = JSON.parse(args);
            const formatted = Object.entries(parsed)
                .map(([key, value]) => `${chalk.cyan(key)}: ${chalk.white(JSON.stringify(value))}`)
                .join(', ');
            return formatted;
        }
        catch {
            return chalk.gray(args);
        }
    }
    /**
     * Format code blocks
     */
    formatCodeBlocks(content) {
        // Format triple backtick code blocks
        content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            const language = lang ? chalk.blue(`[${lang}]`) : '';
            const formattedCode = chalk.gray(code.trim());
            return `\n${language}\n${chalk.bgBlack(formattedCode)}\n`;
        });
        // Format inline code
        content = content.replace(/`([^`]+)`/g, chalk.bgGray.black(' $1 '));
        return content;
    }
    /**
     * Format lists
     */
    formatLists(content) {
        // Format bullet points
        content = content.replace(/^[\s]*[-*+]\s+(.+)$/gm, chalk.cyan('• ') + '$1');
        // Format numbered lists
        content = content.replace(/^[\s]*(\d+)\.\s+(.+)$/gm, chalk.cyan('$1. ') + '$2');
        return content;
    }
    /**
     * Format emphasis
     */
    formatEmphasis(content) {
        // Bold text
        content = content.replace(/\*\*([^*]+)\*\*/g, chalk.bold('$1'));
        // Italic text
        content = content.replace(/\*([^*]+)\*/g, chalk.italic('$1'));
        return content;
    }
    /**
     * Format tool result
     */
    formatToolResult(result) {
        const status = result.success ?
            chalk.green('✅ SUCCESS') :
            chalk.red('❌ FAILED');
        const executionTime = chalk.gray(`(${result.executionTime}ms)`);
        const toolId = chalk.gray(`[${result.toolCallId.slice(0, 8)}]`);
        let output = `${status} ${toolId} ${executionTime}\n`;
        if (result.success) {
            output += chalk.white(result.result);
        }
        else {
            output += chalk.red(result.error || result.result);
        }
        return output;
    }
    /**
     * Format streaming response
     */
    formatStreamingChunk(chunk, isComplete = false) {
        if (isComplete) {
            return chunk + chalk.green(' ✓');
        }
        return chunk + chalk.gray('▋');
    }
    /**
     * Add message to buffer
     */
    addToBuffer(message) {
        this.messageBuffer.push(message);
        // Limit buffer size
        if (this.messageBuffer.length > this.maxBufferSize) {
            this.messageBuffer = this.messageBuffer.slice(-this.maxBufferSize);
        }
        this.emit('message-added', message);
    }
    /**
     * Get message buffer
     */
    getMessageBuffer() {
        return [...this.messageBuffer];
    }
    /**
     * Clear message buffer
     */
    clearBuffer() {
        this.messageBuffer = [];
        this.emit('buffer-cleared');
    }
    /**
     * Export messages as plain text
     */
    exportAsText(includeTimestamps = true) {
        return this.messageBuffer.map(msg => {
            const timestamp = includeTimestamps ?
                `[${format(msg.timestamp, 'yyyy-MM-dd HH:mm:ss')}] ` : '';
            const role = `[${msg.role.toUpperCase()}] `;
            const content = stripAnsi(msg.rawContent);
            return `${timestamp}${role}${content}`;
        }).join('\n\n');
    }
    /**
     * Export messages as JSON
     */
    exportAsJSON() {
        return JSON.stringify(this.messageBuffer.map(msg => ({
            id: msg.id,
            role: msg.role,
            content: msg.rawContent,
            timestamp: msg.timestamp.toISOString(),
            metadata: msg.metadata
        })), null, 2);
    }
    /**
     * Search messages
     */
    searchMessages(query, caseSensitive = false) {
        const searchTerm = caseSensitive ? query : query.toLowerCase();
        return this.messageBuffer.filter(msg => {
            const content = caseSensitive ? msg.rawContent : msg.rawContent.toLowerCase();
            return content.includes(searchTerm);
        });
    }
    /**
     * Get message statistics
     */
    getMessageStats() {
        const messagesByRole = {};
        let totalLength = 0;
        let totalTokens = 0;
        for (const msg of this.messageBuffer) {
            messagesByRole[msg.role] = (messagesByRole[msg.role] || 0) + 1;
            totalLength += msg.rawContent.length;
            if (msg.metadata?.tokenCount) {
                totalTokens += msg.metadata.tokenCount;
            }
        }
        return {
            totalMessages: this.messageBuffer.length,
            messagesByRole,
            averageLength: this.messageBuffer.length > 0 ? Math.round(totalLength / this.messageBuffer.length) : 0,
            totalTokens: totalTokens > 0 ? totalTokens : undefined
        };
    }
    /**
     * Update formatting options
     */
    updateOptions(options) {
        this.options = { ...this.options, ...options };
        this.emit('options-updated', this.options);
    }
}
//# sourceMappingURL=chat-output.js.map