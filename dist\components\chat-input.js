import { EventEmitter } from 'events';
import chalk from 'chalk';
import { nanoid } from 'nanoid';
export class ChatInputProcessor extends EventEmitter {
    inputHistory = [];
    historyIndex = -1;
    currentInput = '';
    options;
    autoCompleteCache = new Map();
    constructor(options = {}) {
        super();
        this.options = {
            placeholder: 'Type your message...',
            maxLength: 4000,
            multiline: false,
            autoComplete: true,
            history: true,
            historySize: 100,
            ...options
        };
    }
    /**
     * Process user input and create a formatted message
     */
    processInput(input) {
        const trimmedInput = input.trim();
        if (!trimmedInput) {
            return null;
        }
        // Validate input
        const validation = this.validateInput(trimmedInput);
        if (!validation.isValid) {
            this.emit('validation-error', validation.error);
            return null;
        }
        // Show warnings if any
        if (validation.warnings && validation.warnings.length > 0) {
            this.emit('validation-warnings', validation.warnings);
        }
        // Add to history
        if (this.options.history) {
            this.addToHistory(trimmedInput);
        }
        // Create message object
        const message = {
            id: nanoid(),
            role: 'user',
            content: trimmedInput,
            timestamp: new Date()
        };
        // Emit processed message
        this.emit('message-processed', message);
        return message;
    }
    /**
     * Validate user input
     */
    validateInput(input) {
        const warnings = [];
        // Check length
        if (input.length > this.options.maxLength) {
            return {
                isValid: false,
                error: `Input too long. Maximum ${this.options.maxLength} characters allowed.`
            };
        }
        // Check for potentially dangerous commands
        const dangerousPatterns = [
            /rm\s+-rf\s+\/|del\s+\/[fs]\s+\/[sq]/i,
            /format\s+[cd]:|mkfs|fdisk/i,
            /shutdown|reboot|halt|poweroff/i,
            /dd\s+if=\/dev\/zero/i
        ];
        for (const pattern of dangerousPatterns) {
            if (pattern.test(input)) {
                warnings.push('⚠️  Potentially dangerous command detected. Please be careful.');
                break;
            }
        }
        // Check for very long commands
        if (input.length > 500) {
            warnings.push('📝 Very long input detected. Consider breaking it into smaller parts.');
        }
        // Check for multiple commands
        const commandSeparators = /[;&|]+/g;
        const matches = input.match(commandSeparators);
        if (matches && matches.length > 3) {
            warnings.push('🔗 Multiple commands detected. They will be executed in sequence.');
        }
        return {
            isValid: true,
            warnings
        };
    }
    /**
     * Add input to history
     */
    addToHistory(input) {
        // Remove if already exists
        const existingIndex = this.inputHistory.indexOf(input);
        if (existingIndex !== -1) {
            this.inputHistory.splice(existingIndex, 1);
        }
        // Add to beginning
        this.inputHistory.unshift(input);
        // Limit history size
        if (this.inputHistory.length > this.options.historySize) {
            this.inputHistory = this.inputHistory.slice(0, this.options.historySize);
        }
        // Reset history index
        this.historyIndex = -1;
    }
    /**
     * Get previous input from history
     */
    getPreviousInput() {
        if (!this.options.history || this.inputHistory.length === 0) {
            return null;
        }
        if (this.historyIndex < this.inputHistory.length - 1) {
            this.historyIndex++;
            return this.inputHistory[this.historyIndex];
        }
        return null;
    }
    /**
     * Get next input from history
     */
    getNextInput() {
        if (!this.options.history || this.historyIndex <= 0) {
            this.historyIndex = -1;
            return '';
        }
        this.historyIndex--;
        return this.inputHistory[this.historyIndex];
    }
    /**
     * Get auto-complete suggestions
     */
    getAutoCompleteSuggestions(input) {
        if (!this.options.autoComplete || input.length < 2) {
            return [];
        }
        const suggestions = [];
        const lowerInput = input.toLowerCase();
        // Check history for matches
        for (const historyItem of this.inputHistory) {
            if (historyItem.toLowerCase().startsWith(lowerInput) && historyItem !== input) {
                suggestions.push(historyItem);
            }
        }
        // Check cache for command suggestions
        const cacheKey = input.split(' ')[0];
        const cachedSuggestions = this.autoCompleteCache.get(cacheKey);
        if (cachedSuggestions) {
            suggestions.push(...cachedSuggestions.filter(s => s.toLowerCase().startsWith(lowerInput) && !suggestions.includes(s)));
        }
        return suggestions.slice(0, 5); // Limit to 5 suggestions
    }
    /**
     * Add auto-complete suggestions to cache
     */
    addAutoCompleteSuggestions(command, suggestions) {
        this.autoCompleteCache.set(command, suggestions);
    }
    /**
     * Format input for display
     */
    formatInputForDisplay(input) {
        // Highlight slash commands
        const slashCommandRegex = /^\/(\w+)/;
        const slashMatch = input.match(slashCommandRegex);
        if (slashMatch) {
            return chalk.cyan(slashMatch[0]) + input.slice(slashMatch[0].length);
        }
        // Highlight shell commands
        const shellCommandRegex = /^(\w+)/;
        const shellMatch = input.match(shellCommandRegex);
        if (shellMatch) {
            return chalk.green(shellMatch[0]) + input.slice(shellMatch[0].length);
        }
        return input;
    }
    /**
     * Get input statistics
     */
    getInputStats() {
        const commandCounts = new Map();
        let totalLength = 0;
        for (const input of this.inputHistory) {
            totalLength += input.length;
            const command = input.split(' ')[0];
            commandCounts.set(command, (commandCounts.get(command) || 0) + 1);
        }
        const mostUsedCommands = Array.from(commandCounts.entries())
            .map(([command, count]) => ({ command, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);
        return {
            historySize: this.inputHistory.length,
            averageLength: this.inputHistory.length > 0 ? Math.round(totalLength / this.inputHistory.length) : 0,
            mostUsedCommands
        };
    }
    /**
     * Clear input history
     */
    clearHistory() {
        this.inputHistory = [];
        this.historyIndex = -1;
        this.emit('history-cleared');
    }
    /**
     * Export input history
     */
    exportHistory() {
        return [...this.inputHistory];
    }
    /**
     * Import input history
     */
    importHistory(history) {
        this.inputHistory = history.slice(0, this.options.historySize);
        this.historyIndex = -1;
        this.emit('history-imported', this.inputHistory.length);
    }
    /**
     * Set current input (for external updates)
     */
    setCurrentInput(input) {
        this.currentInput = input;
    }
    /**
     * Get current input
     */
    getCurrentInput() {
        return this.currentInput;
    }
}
//# sourceMappingURL=chat-input.js.map