export interface FormatOptions {
    maxWidth?: number;
    indent?: number;
    showLineNumbers?: boolean;
    highlightSyntax?: boolean;
    wrapText?: boolean;
}
export declare class OutputFormatters {
    /**
     * Format command output with syntax highlighting
     */
    static formatCommandOutput(output: string, command: string, options?: FormatOptions): string;
    /**
     * Apply syntax highlighting based on command type and content
     */
    private static applySyntaxHighlighting;
    /**
     * Highlight file listing output
     */
    private static highlightFileList;
    /**
     * Highlight Git output
     */
    private static highlightGitOutput;
    /**
     * Highlight process listing
     */
    private static highlightProcessList;
    /**
     * Highlight network output
     */
    private static highlightNetworkOutput;
    /**
     * Highlight log output
     */
    private static highlightLogOutput;
    /**
     * Check if line contains JSON
     */
    private static isJsonLine;
    /**
     * Highlight JSON content
     */
    private static highlightJson;
    /**
     * Format JSON object with colors
     */
    private static formatJsonObject;
    /**
     * Wrap text to specified width
     */
    private static wrapText;
    /**
     * Format table data
     */
    static formatTable(data: string[][], headers?: string[], options?: {
        border?: boolean;
        padding?: number;
        alignment?: ('left' | 'center' | 'right')[];
    }): string;
    /**
     * Format progress bar
     */
    static formatProgressBar(current: number, total: number, width?: number, options?: {
        showPercentage?: boolean;
        showNumbers?: boolean;
        style?: 'blocks' | 'bars' | 'dots';
    }): string;
    /**
     * Format file size
     */
    static formatFileSize(bytes: number): string;
    /**
     * Format duration
     */
    static formatDuration(milliseconds: number): string;
    /**
     * Format timestamp
     */
    static formatTimestamp(date: Date, style?: 'short' | 'medium' | 'long'): string;
}
//# sourceMappingURL=formatters.d.ts.map