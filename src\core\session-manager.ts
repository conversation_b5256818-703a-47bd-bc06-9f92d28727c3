import { TerminalSession, LLMMessage, ArienError } from '@/types';
import { nanoid } from 'nanoid';
import { ConfigManager } from './config';
import Conf from 'conf';

export class SessionManager {
  private sessions: Map<string, TerminalSession> = new Map();
  private currentSessionId: string | null = null;
  private configManager: ConfigManager;
  private sessionStore: Conf<any>;

  constructor() {
    this.configManager = ConfigManager.getInstance();
    this.sessionStore = new Conf({
      projectName: 'arien-ai-cli-sessions',
      schema: {
        sessions: {
          type: 'object',
          default: {}
        }
      }
    });

    this.loadSessions();
  }

  private loadSessions(): void {
    const storedSessions = this.sessionStore.get('sessions') || {};

    Object.entries(storedSessions).forEach(([id, sessionData]) => {
      // Convert date strings back to Date objects
      const data = sessionData as any;
      const session: TerminalSession = {
        id: data.id,
        name: data.name,
        workingDirectory: data.workingDirectory,
        provider: data.provider,
        model: data.model,
        createdAt: new Date(data.createdAt),
        updatedAt: new Date(data.updatedAt),
        messages: (data.messages || []).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      };

      this.sessions.set(id, session);
    });
  }

  private saveSessions(): void {
    const sessionsToStore: Record<string, TerminalSession> = {};
    
    this.sessions.forEach((session, id) => {
      sessionsToStore[id] = session;
    });
    
    this.sessionStore.set('sessions', sessionsToStore);
  }

  public createSession(name?: string): TerminalSession {
    const config = this.configManager.getAll();
    const sessionId = nanoid();
    const sessionName = name || `Session ${new Date().toLocaleString()}`;
    
    const session: TerminalSession = {
      id: sessionId,
      name: sessionName,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      workingDirectory: config.workingDirectory || process.cwd(),
      provider: config.provider,
      model: config.model
    };

    this.sessions.set(sessionId, session);
    this.currentSessionId = sessionId;
    this.configManager.set('sessionId', sessionId);
    this.saveSessions();

    return session;
  }

  public getCurrentSession(): TerminalSession | null {
    if (!this.currentSessionId) {
      return null;
    }
    
    return this.sessions.get(this.currentSessionId) || null;
  }

  public setCurrentSession(sessionId: string): TerminalSession {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new ArienError(`Session with ID ${sessionId} not found`, 'SESSION_NOT_FOUND');
    }

    this.currentSessionId = sessionId;
    this.configManager.set('sessionId', sessionId);
    
    return session;
  }

  public getAllSessions(): TerminalSession[] {
    return Array.from(this.sessions.values()).sort(
      (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
    );
  }

  public getSession(sessionId: string): TerminalSession | null {
    return this.sessions.get(sessionId) || null;
  }

  public deleteSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    this.sessions.delete(sessionId);
    
    // If this was the current session, clear it
    if (this.currentSessionId === sessionId) {
      this.currentSessionId = null;
      this.configManager.delete('sessionId');
    }

    this.saveSessions();
    return true;
  }

  public updateSessionName(sessionId: string, name: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    session.name = name;
    session.updatedAt = new Date();
    this.saveSessions();
    
    return true;
  }

  public addMessage(message: LLMMessage, sessionId?: string): void {
    const targetSessionId = sessionId || this.currentSessionId;
    if (!targetSessionId) {
      throw new ArienError('No active session to add message to', 'NO_ACTIVE_SESSION');
    }

    const session = this.sessions.get(targetSessionId);
    if (!session) {
      throw new ArienError(`Session with ID ${targetSessionId} not found`, 'SESSION_NOT_FOUND');
    }

    session.messages.push(message);
    session.updatedAt = new Date();
    this.saveSessions();
  }

  public getMessages(sessionId?: string): LLMMessage[] {
    const targetSessionId = sessionId || this.currentSessionId;
    if (!targetSessionId) {
      return [];
    }

    const session = this.sessions.get(targetSessionId);
    return session ? session.messages : [];
  }

  public clearMessages(sessionId?: string): void {
    const targetSessionId = sessionId || this.currentSessionId;
    if (!targetSessionId) {
      return;
    }

    const session = this.sessions.get(targetSessionId);
    if (session) {
      session.messages = [];
      session.updatedAt = new Date();
      this.saveSessions();
    }
  }

  public getMessageHistory(limit?: number, sessionId?: string): LLMMessage[] {
    const messages = this.getMessages(sessionId);
    
    if (limit && limit > 0) {
      return messages.slice(-limit);
    }
    
    return messages;
  }

  public searchMessages(query: string, sessionId?: string): LLMMessage[] {
    const messages = this.getMessages(sessionId);
    const lowercaseQuery = query.toLowerCase();
    
    return messages.filter(message => 
      message.content.toLowerCase().includes(lowercaseQuery)
    );
  }

  public exportSession(sessionId: string): string {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new ArienError(`Session with ID ${sessionId} not found`, 'SESSION_NOT_FOUND');
    }

    return JSON.stringify(session, null, 2);
  }

  public importSession(sessionData: string): TerminalSession {
    try {
      const parsedSession = JSON.parse(sessionData);
      
      // Validate and convert the session data
      const session: TerminalSession = {
        id: parsedSession.id || nanoid(),
        name: parsedSession.name || 'Imported Session',
        messages: (parsedSession.messages || []).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        })),
        createdAt: new Date(parsedSession.createdAt || Date.now()),
        updatedAt: new Date(parsedSession.updatedAt || Date.now()),
        workingDirectory: parsedSession.workingDirectory || process.cwd(),
        provider: parsedSession.provider || 'deepseek',
        model: parsedSession.model || 'deepseek-chat'
      };

      this.sessions.set(session.id, session);
      this.saveSessions();
      
      return session;
    } catch (error) {
      throw new ArienError(
        `Failed to import session: ${error instanceof Error ? error.message : 'Invalid JSON'}`,
        'IMPORT_ERROR'
      );
    }
  }

  public getSessionStats(sessionId?: string): {
    messageCount: number;
    userMessages: number;
    assistantMessages: number;
    toolCalls: number;
    firstMessage?: Date;
    lastMessage?: Date;
  } {
    const messages = this.getMessages(sessionId);
    
    const stats = {
      messageCount: messages.length,
      userMessages: messages.filter(m => m.role === 'user').length,
      assistantMessages: messages.filter(m => m.role === 'assistant').length,
      toolCalls: messages.reduce((count, m) => count + (m.toolCalls?.length || 0), 0),
      firstMessage: messages.length > 0 ? messages[0]?.timestamp : undefined,
      lastMessage: messages.length > 0 ? messages[messages.length - 1]?.timestamp : undefined
    };

    return stats;
  }

  public cleanupOldSessions(maxAge: number = 30 * 24 * 60 * 60 * 1000): number {
    const cutoffDate = new Date(Date.now() - maxAge);
    let deletedCount = 0;

    this.sessions.forEach((session, id) => {
      if (session.updatedAt < cutoffDate) {
        this.sessions.delete(id);
        deletedCount++;
      }
    });

    if (deletedCount > 0) {
      this.saveSessions();
    }

    return deletedCount;
  }

  public ensureCurrentSession(): TerminalSession {
    let session = this.getCurrentSession();
    
    if (!session) {
      session = this.createSession();
    }
    
    return session;
  }
}
