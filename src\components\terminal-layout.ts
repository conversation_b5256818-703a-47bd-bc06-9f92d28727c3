import blessed from 'blessed';
import { TerminalState, ComponentProps } from '@/types';
import chalk from 'chalk';

export interface TerminalLayoutOptions {
  title?: string;
  border?: boolean;
  scrollable?: boolean;
  mouse?: boolean;
  keys?: boolean;
}

export class TerminalLayout {
  private screen: blessed.Widgets.Screen;
  private headerBox: any;
  private chatBox: any;
  private inputBox: any;
  private statusBox: any;
  private sidebarBox: any;
  private mainContainer: any;
  private showSidebar: boolean = false;

  constructor(options: TerminalLayoutOptions = {}) {
    this.screen = blessed.screen({
      smartCSR: true,
      title: options.title || 'Arien AI CLI',
      cursor: {
        artificial: true,
        shape: 'line',
        blink: true,
        color: 'white'
      },
      debug: false,
      dockBorders: false,
      fullUnicode: true,
      autoPadding: true
    });

    this.setupLayout();
    this.setupEventHandlers();
  }

  private setupLayout(): void {
    // Header box - shows current session info
    this.headerBox = blessed.box({
      parent: this.screen,
      top: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
        fg: 6
      },
      style: {
        fg: 'white',
        bg: 'black',
        border: {
          fg: 'cyan'
        }
      },
      tags: true,
      content: this.getHeaderContent()
    });

    // Main container for chat and sidebar
    this.mainContainer = blessed.box({
      parent: this.screen,
      top: 3,
      left: 0,
      width: '100%',
      height: '100%-6',
      style: {
        bg: 'black'
      }
    });

    // Sidebar for session management (initially hidden)
    this.sidebarBox = blessed.box({
      parent: this.mainContainer,
      top: 0,
      left: 0,
      width: this.showSidebar ? '25%' : 0,
      height: '100%',
      border: this.showSidebar ? {
        type: 'line',
        fg: 8
      } : undefined,
      style: {
        fg: 'white',
        bg: 'black',
        border: {
          fg: 'gray'
        }
      },
      tags: true,
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      hidden: !this.showSidebar
    });

    // Chat box - main conversation area
    this.chatBox = blessed.box({
      parent: this.mainContainer,
      top: 0,
      left: this.showSidebar ? '25%' : 0,
      width: this.showSidebar ? '75%' : '100%',
      height: '100%',
      border: {
        type: 'line',
        fg: 4
      },
      style: {
        fg: 'white',
        bg: 'black',
        border: {
          fg: 'blue'
        }
      },
      tags: true,
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'gray'
        },
        style: {
          inverse: true
        }
      }
    });

    // Input box - user input area
    this.inputBox = blessed.textbox({
      parent: this.screen,
      bottom: 3,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
        fg: 2
      },
      style: {
        fg: 'white',
        bg: 'black',
        border: {
          fg: 'green'
        }
      },
      tags: true,
      inputOnFocus: true,
      mouse: true,
      keys: true
    });

    // Status box - shows current operation status
    this.statusBox = blessed.box({
      parent: this.screen,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
        fg: 3
      },
      style: {
        fg: 'white',
        bg: 'black',
        border: {
          fg: 'yellow'
        }
      },
      tags: true,
      content: this.getStatusContent()
    });
  }

  private setupEventHandlers(): void {
    // Global key handlers
    this.screen.key(['escape', 'q', 'C-c'], () => {
      return process.exit(0);
    });

    // Toggle sidebar
    this.screen.key(['C-s'], () => {
      this.toggleSidebar();
    });

    // Focus management
    this.screen.key(['tab'], () => {
      this.focusNext();
    });

    this.screen.key(['S-tab'], () => {
      this.focusPrevious();
    });

    // Input box handlers
    this.inputBox.key(['enter'], () => {
      const input = this.inputBox.getValue();
      if (input.trim()) {
        this.handleUserInput(input);
        this.inputBox.clearValue();
      }
    });

    // Chat box scroll handlers
    this.chatBox.key(['up'], () => {
      this.chatBox.scroll(-1);
      this.screen.render();
    });

    this.chatBox.key(['down'], () => {
      this.chatBox.scroll(1);
      this.screen.render();
    });

    this.chatBox.key(['pageup'], () => {
      this.chatBox.scroll(-10);
      this.screen.render();
    });

    this.chatBox.key(['pagedown'], () => {
      this.chatBox.scroll(10);
      this.screen.render();
    });

    // Auto-focus input box
    this.inputBox.focus();
  }

  private getHeaderContent(): string {
    return `{center}{bold}Arien AI CLI{/bold}{/center}
{left}Session: Loading... | Provider: Loading... | Model: Loading...{/left}`;
  }

  private getStatusContent(): string {
    return `{left}Ready | Working Directory: ${process.cwd()}{/left}`;
  }

  public updateHeader(state: TerminalState): void {
    const session = state.currentSession;
    const sessionName = session ? session.name : 'No Session';
    const provider = state.config.provider || 'None';
    const model = state.config.model || 'None';
    const workingDir = session ? session.workingDirectory : process.cwd();

    const content = `{center}{bold}Arien AI CLI{/bold}{/center}
{left}Session: ${sessionName} | Provider: ${provider} | Model: ${model} | Dir: ${this.truncatePath(workingDir)}{/left}`;

    this.headerBox.setContent(content);
    this.screen.render();
  }

  public updateStatus(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    const colors = {
      info: 'white',
      success: 'green',
      warning: 'yellow',
      error: 'red'
    };

    const content = `{left}{${colors[type]}}${message}{/${colors[type]}}{/left}`;
    this.statusBox.setContent(content);
    this.screen.render();
  }

  public addMessage(role: 'user' | 'assistant' | 'system' | 'tool', content: string, timestamp?: Date): void {
    const time = timestamp ? timestamp.toLocaleTimeString() : new Date().toLocaleTimeString();
    const roleColors = {
      user: 'cyan',
      assistant: 'green',
      system: 'yellow',
      tool: 'magenta'
    };

    const roleColor = roleColors[role];
    const formattedMessage = `{${roleColor}}[${time}] ${role.toUpperCase()}:{/${roleColor}} ${content}\n`;

    this.chatBox.pushLine(formattedMessage);
    this.chatBox.setScrollPerc(100);
    this.screen.render();
  }

  public clearChat(): void {
    this.chatBox.setContent('');
    this.screen.render();
  }

  public showSpinner(message: string): void {
    this.updateStatus(`⠋ ${message}`, 'info');
  }

  public hideSpinner(): void {
    this.updateStatus('Ready', 'info');
  }

  public toggleSidebar(): void {
    this.showSidebar = !this.showSidebar;
    
    if (this.showSidebar) {
      this.sidebarBox.width = '25%';
      this.sidebarBox.show();
      this.chatBox.left = '25%';
      this.chatBox.width = '75%';
    } else {
      this.sidebarBox.width = 0;
      this.sidebarBox.hide();
      this.chatBox.left = 0;
      this.chatBox.width = '100%';
    }

    this.screen.render();
  }

  public updateSidebar(sessions: any[]): void {
    if (!this.showSidebar) return;

    let content = '{bold}Sessions{/bold}\n\n';
    sessions.forEach((session, index) => {
      const isActive = session.isActive ? '{green}● {/green}' : '  ';
      content += `${isActive}${index + 1}. ${session.name}\n`;
    });

    this.sidebarBox.setContent(content);
    this.screen.render();
  }

  private focusNext(): void {
    const focusables = [this.inputBox, this.chatBox, this.sidebarBox];
    const current = this.screen.focused;
    const currentIndex = focusables.indexOf(current as any);
    const nextIndex = (currentIndex + 1) % focusables.length;
    
    if (nextIndex === 2 && !this.showSidebar) {
      focusables[0].focus();
    } else {
      focusables[nextIndex].focus();
    }
  }

  private focusPrevious(): void {
    const focusables = [this.inputBox, this.chatBox, this.sidebarBox];
    const current = this.screen.focused;
    const currentIndex = focusables.indexOf(current as any);
    const prevIndex = currentIndex === 0 ? focusables.length - 1 : currentIndex - 1;
    
    if (prevIndex === 2 && !this.showSidebar) {
      focusables[1].focus();
    } else {
      focusables[prevIndex].focus();
    }
  }

  private truncatePath(path: string, maxLength: number = 30): string {
    if (path.length <= maxLength) return path;
    return '...' + path.slice(-(maxLength - 3));
  }

  private handleUserInput(input: string): void {
    // This will be handled by the main terminal component
    this.screen.emit('user-input', input);
  }

  public render(): void {
    this.screen.render();
  }

  public destroy(): void {
    this.screen.destroy();
  }

  public getScreen(): blessed.Widgets.Screen {
    return this.screen;
  }

  public focusInput(): void {
    this.inputBox.focus();
  }

  public setInputPlaceholder(text: string): void {
    // Blessed doesn't have built-in placeholder support, but we can simulate it
    if (!this.inputBox.getValue()) {
      this.inputBox.setValue(`{gray}${text}{/gray}`);
    }
  }

  public showError(error: string): void {
    this.addMessage('system', `{red}Error: ${error}{/red}`);
  }

  public showSuccess(message: string): void {
    this.addMessage('system', `{green}Success: ${message}{/green}`);
  }

  public showWarning(message: string): void {
    this.addMessage('system', `{yellow}Warning: ${message}{/yellow}`);
  }

  public showInfo(message: string): void {
    this.addMessage('system', `{blue}Info: ${message}{/blue}`);
  }
}
