import { TerminalSession, LLMMessage } from '@/types';
export declare class SessionManager {
    private sessions;
    private currentSessionId;
    private configManager;
    private sessionStore;
    constructor();
    private loadSessions;
    private saveSessions;
    createSession(name?: string): TerminalSession;
    getCurrentSession(): TerminalSession | null;
    setCurrentSession(sessionId: string): TerminalSession;
    getAllSessions(): TerminalSession[];
    getSession(sessionId: string): TerminalSession | null;
    deleteSession(sessionId: string): boolean;
    updateSessionName(sessionId: string, name: string): boolean;
    addMessage(message: LLMMessage, sessionId?: string): void;
    getMessages(sessionId?: string): LLMMessage[];
    clearMessages(sessionId?: string): void;
    getMessageHistory(limit?: number, sessionId?: string): LLMMessage[];
    searchMessages(query: string, sessionId?: string): LLMMessage[];
    exportSession(sessionId: string): string;
    importSession(sessionData: string): TerminalSession;
    getSessionStats(sessionId?: string): {
        messageCount: number;
        userMessages: number;
        assistantMessages: number;
        toolCalls: number;
        firstMessage?: Date;
        lastMessage?: Date;
    };
    cleanupOldSessions(maxAge?: number): number;
    ensureCurrentSession(): TerminalSession;
}
//# sourceMappingURL=session-manager.d.ts.map