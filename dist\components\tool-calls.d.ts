import { EventEmitter } from 'events';
import { Tool<PERSON>all, ToolResult, ToolDefinition } from '@/types';
export interface ToolExecutionOptions {
    parallel?: boolean;
    maxConcurrency?: number;
    timeout?: number;
    retryAttempts?: number;
    confirmBeforeExecution?: boolean;
    showProgress?: boolean;
}
export interface ToolExecutionContext {
    id: string;
    toolCall: ToolCall;
    startTime: Date;
    endTime?: Date;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    result?: ToolResult;
    error?: Error;
    retryCount: number;
    spinner?: any;
}
export interface ToolExecutionPlan {
    id: string;
    toolCalls: ToolCall[];
    executionOrder: ToolCall[][];
    estimatedDuration: number;
    riskLevel: 'low' | 'medium' | 'high';
    warnings: string[];
}
export declare class ToolCallsProcessor extends EventEmitter {
    private executionContexts;
    private toolDefinitions;
    private defaultOptions;
    constructor(options?: ToolExecutionOptions);
    /**
     * Register tool definitions for better processing
     */
    registerToolDefinition(definition: ToolDefinition): void;
    /**
     * Create execution plan for tool calls
     */
    createExecutionPlan(toolCalls: ToolCall[]): ToolExecutionPlan;
    /**
     * Analyze tool calls for execution order and risks
     */
    private analyzeToolCalls;
    /**
     * Determine optimal execution order
     */
    private determineExecutionOrder;
    /**
     * Estimate execution time
     */
    private estimateExecutionTime;
    /**
     * Execute tool calls according to plan
     */
    executeToolCalls(toolCalls: ToolCall[], executor: (toolCall: ToolCall) => Promise<ToolResult>, options?: Partial<ToolExecutionOptions>): Promise<ToolResult[]>;
    /**
     * Execute a single tool with full context tracking
     */
    private executeSingleTool;
    /**
     * Execute tool group in parallel
     */
    private executeToolGroup;
    /**
     * Execute with retry logic
     */
    private executeWithRetry;
    /**
     * Check if error is retryable
     */
    private isRetryableError;
    /**
     * Request user confirmation
     */
    private requestConfirmation;
    /**
     * Format tool call for display
     */
    formatToolCall(toolCall: ToolCall): string;
    /**
     * Format tool result for display
     */
    formatToolResult(result: ToolResult): string;
    /**
     * Format success output
     */
    private formatSuccessOutput;
    /**
     * Format error output
     */
    private formatErrorOutput;
    /**
     * Get execution statistics
     */
    getExecutionStats(): {
        totalExecutions: number;
        successRate: number;
        averageExecutionTime: number;
        mostUsedTools: Array<{
            name: string;
            count: number;
        }>;
    };
    /**
     * Cancel all running executions
     */
    cancelAllExecutions(): void;
    /**
     * Get active executions
     */
    getActiveExecutions(): ToolExecutionContext[];
}
//# sourceMappingURL=tool-calls.d.ts.map