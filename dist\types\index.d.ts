import { z } from 'zod';
export interface LLMProvider {
    name: string;
    models: string[];
    apiKey?: string;
    baseUrl?: string;
    isConfigured: boolean;
}
export interface LLMMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    toolCalls?: ToolCall[];
    toolCallId?: string;
    timestamp: Date;
    id: string;
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface ToolResult {
    toolCallId: string;
    result: string;
    success: boolean;
    error?: string;
    executionTime: number;
}
export interface ShellCommandResult {
    stdout: string;
    stderr: string;
    exitCode: number;
    command: string;
    executionTime: number;
    success: boolean;
}
export declare const ConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["deepseek", "ollama"]>;
    model: z.ZodString;
    apiKey: z.ZodOptional<z.ZodString>;
    baseUrl: z.ZodOptional<z.ZodString>;
    maxTokens: z.ZodDefault<z.ZodNumber>;
    temperature: z.Zod<PERSON>efault<z.ZodNumber>;
    sessionId: z.Zod<PERSON>ptional<z.ZodString>;
    workingDirectory: z.ZodOptional<z.ZodString>;
    autoApprove: z.ZodDefault<z.ZodBoolean>;
    retryAttempts: z.ZodDefault<z.ZodNumber>;
    timeout: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    provider: "deepseek" | "ollama";
    model: string;
    maxTokens: number;
    temperature: number;
    autoApprove: boolean;
    retryAttempts: number;
    timeout: number;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    sessionId?: string | undefined;
    workingDirectory?: string | undefined;
}, {
    provider: "deepseek" | "ollama";
    model: string;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    maxTokens?: number | undefined;
    temperature?: number | undefined;
    sessionId?: string | undefined;
    workingDirectory?: string | undefined;
    autoApprove?: boolean | undefined;
    retryAttempts?: number | undefined;
    timeout?: number | undefined;
}>;
export type Config = z.infer<typeof ConfigSchema>;
export interface TerminalSession {
    id: string;
    name: string;
    messages: LLMMessage[];
    createdAt: Date;
    updatedAt: Date;
    workingDirectory: string;
    provider: string;
    model: string;
}
export interface TerminalState {
    currentSession: TerminalSession | null;
    sessions: TerminalSession[];
    config: Config;
    isProcessing: boolean;
    currentCommand: string;
    messageHistory: LLMMessage[];
}
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required: string[];
    };
    usage: {
        when: string;
        whenNot: string;
        parallel: boolean;
        sequential: boolean;
        examples: string[];
    };
}
export declare class ArienError extends Error {
    code: string;
    details?: any | undefined;
    constructor(message: string, code: string, details?: any | undefined);
}
export declare class ProviderError extends ArienError {
    constructor(message: string, details?: any);
}
export declare class CommandError extends ArienError {
    constructor(message: string, details?: any);
}
export declare class ConfigError extends ArienError {
    constructor(message: string, details?: any);
}
export interface TerminalEvent {
    type: 'message' | 'command' | 'tool_call' | 'error' | 'config_change';
    data: any;
    timestamp: Date;
}
export interface SlashCommand {
    name: string;
    description: string;
    aliases: string[];
    handler: (args: string[]) => Promise<void>;
    usage: string;
}
export interface ComponentProps {
    state: TerminalState;
    onStateChange: (newState: Partial<TerminalState>) => void;
    onEvent: (event: TerminalEvent) => void;
}
//# sourceMappingURL=index.d.ts.map